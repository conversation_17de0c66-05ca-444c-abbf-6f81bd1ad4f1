name: Deploy to Server

on:
  push:
    branches:
      -  master


jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      -  name: Checkout code
         uses: actions/checkout@v2

      -  name: Set up Node.js
         uses: actions/setup-node@v2
         with:
           node-version: '16'

      -  name: Install dependencies
          run: npm install

      -  name: Build project
          run: npm run build

      -  name: Deploy to server
          env:
            SSH_PRIVATE_KEY: ${{ secrets.SSH_PRIVATE_KEY }}
            SERVER: ${{ secrets.SERVER }}
            USERNAME: ${{ secrets.USERNAME }}
            run: |
                ssh -o StrictHostKeyChecking=no $USERNAME@$SERVER 'cd /home/<USER>/project && git pull && npm install && npm run build && pm2 restart all'