37.32.4.150  api.voopee.co

sudo apt update
sudo apt install curl
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs
بررسی نصب صحیح 
node -v
npm -v

git clone https://github.com/parvane69/word-maker.git
username mikhad mizani parvane69
password : ****************************************
بعد پروژه رو میگیره میتونی با فایل زیلا ببینی که گرفته 

cd word-maker
npm install --force


sudo apt update
sudo apt install docker.io
sudo usermod -aG docker ubuntu
exit
دوباره وارد شو
docker --version
sudo systemctl restart docker

بعد فایل redis-image.tar رو آپلود کردم تو همون پوشه روژه کنار فایل src

docker load -i redis_image.tar
docker run -d --name redis_container -p 6379:6379 redis

sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
sudo -u postgres psql

CREATE DATABASE wordmaker;
ALTER USER postgres WITH PASSWORD 'LwuYoMu5@VNJ%aDJhCJzok';
GRANT ALL PRIVILEGES ON DATABASE wordmaker TO postgres;

تا اینجا باید روی این آدرس بیاد بالا 
http://37.32.4.150:3002/api/docs
https://api.voopee.co/api/docs

راه اندازی آپاچی
sudo apt update
sudo apt install apache2


۶. نصب و پیکربندی mod_proxy
برای اینکه آپاچی درخواستها را به برنامه NestJS شما فوروارد کند، باید mod_proxy را فعال کنید:

sudo a2enmod proxy
sudo a2enmod proxy_http

دوبار این دستورو زدم دفعه اول خطا داشت
sudo systemctl restart apache2
sudo systemctl restart apache2

۷. پیکربندی آپاچی برای پروژه NestJS
یک فایل پیکربندی جدید برای سایت خود ایجاد کنید:

sudo nano /etc/apache2/sites-available/nestjs.conf

محتوای زیر را در فایل قرار دهید:



<VirtualHost *:80>
ServerName apiv2.voopee.co

ProxyPreserveHost On
ProxyPass / http://localhost:3002/
ProxyPassReverse / http://localhost:3002/

ErrorLog ${APACHE_LOG_DIR}/error.log
CustomLog ${APACHE_LOG_DIR}/access.log combined
</VirtualHost>



۸. فعالسازی سایت و راهاندازی مجدد آپاچی
سایت جدید را فعال کنید و آپاچی را راهاندازی مجدد کنید:

sudo a2ensite nestjs.conf
sudo systemctl restart apache2

۹. اطمینان از اجرای پروژه
مطمئن شوید که پروژه NestJS شما در حال اجرا است:

npm run start


حالا نصب pm2
sudo npm install -g pm2
pm2 start dist/main.js --name "word-maker"
pm2 save
pm2 startup



فایل env رو یه تغییری دادم ولی اعمال نمیشد 
چیکار کردم که درست شد ؟ 

rm -rf dist
rm -rf src
unzip word-maker-3.zip


sudo service apache2 stop
sudo lsof -i :3002
sudo kill -9 720738
sudo service apache2 start

PORT=3003 npm run start



