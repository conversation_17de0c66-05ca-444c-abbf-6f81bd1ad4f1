توجه شدم. برای اینکه سرور شما بعد از بستن PuTTY همچنان در دسترس باشد، باید برنامه خود را به صورت یک سرویس یا در پسزمینه اجرا کنید. این کار را میتوانید با استفاده از ابزارهایی مانند pm2 یا screen انجام دهید.

استفاده از pm2
pm2 یک مدیر فرآیند برای Node.js است که به شما اجازه میدهد برنامههای خود را به صورت سرویس اجرا کنید. برای نصب و استفاده از pm2، مراحل زیر را دنبال کنید:

1. 
نصب pm2:

npm install -g pm2

1. 
اجرای برنامه با pm2:

pm2 start dist/main.js --name "my-nest-app"

1. 
ذخیره تنظیمات pm2 برای راهاندازی مجدد خودکار:

pm2 save
pm2 startup

استفاده از screen
screen یک ابزار ترمینال است که به شما اجازه میدهد جلسات ترمینال را در پسزمینه اجرا کنید.

1. 
نصب screen (اگر نصب نیست):

sudo apt-get install screen

1. 
ایجاد یک جلسه جدید screen:

screen -S my-nest-app

1. 
اجرای برنامه در جلسه screen:

npm run start:prod

1. 
خروج از جلسه screen بدون بستن آن:

Ctrl + A, سپس D

با استفاده از این روشها، برنامه شما حتی پس از بستن PuTTY همچنان در حال اجرا خواهد بود و همکارتان میتواند به آدرس مورد نظر دسترسی داشته باشد.

اگر سوال دیگری دارید یا نیاز به کمک بیشتری دارید، خوشحال میشوم کمک کنم! blush

اگه یه موقعی پروژه به خاطر redis از کار افتاده بود 
با این دستور چک کن ببین ران هست یا نه 
docker ps
اگه نبود این دستورو بنویس تا ران بشه دوباره 
sudo docker start redis-container

http://***********/


apache : => ***********
"14379737598"
rm -rf dist
rm -rf src
unzip 
pm2 list

pm2 stop process_id
یا 
pm2 delete process_id

pm2 start npm --name "word-maker-1" -- run start
pm2 save
pm2 startup
اگه این خطا داد این 

sudo env PATH=$PATH:/usr/bin /usr/lib/node_modules/pm2/bin/pm2 startup systemd -u ubuntu --hp /home/<USER>
دستورات اتصال به دیتابیس 
sudo -i -u postgres
psql
\c wordmaker
update products set accessLevel = '2';
delete from products where id = 2;
update users set email = '<EMAIL>' where id = 19;
update purchases set product_id = 2;
update products set description = 'دارای دسترسی تا مرحله آخر' where id = 2;
update products set currency= 'IRR' where id = 1;
update products set "restrictedAccessLevels" = null;
select * from products;
select * from bazar;
select * from purchases;
select * from users;
select * from users where id=21;
select * from users ORDER BY created_at DESC;
select * from logs ORDER BY created_at DESC;
SELECT COUNT(*) FROM purchases;
select * from purchases ORDER BY time DESC;
SELECT * FROM logs ORDER BY id DESC LIMIT 10;
SELECT * FROM logs 
WHERE "message" NOT LIKE '%Unauthorized%' 
ORDER BY id DESC 
LIMIT 50;
دستوراتت که تموم شد 2 بار exit روب زن 
تا وقتی بزن که وقتی نوشتی whoami بنویسه ubuntu
اینطوری میتونی به پوشه ubuntu/project بری وگرنه permission denied میگیری

به نظر میرسد که دایرکتوری /home/<USER>

cd /home/<USER>

اگر همچنان مشکل دارید، ممکن است مشکل از شل یا تنظیمات محیطی شما باشد. برای بررسی بیشتر، میتوانید از دستور زیر استفاده کنید تا مطمئن شوید که دایرکتوری وجود دارد و شما دسترسی لازم را دارید:

ls -ld /home/<USER>

همچنین میتوانید از دستور pwd برای بررسی مسیر فعلی خود استفاده کنید:

pwd

اگر همچنان مشکل دارید، لطفاً خروجی دستورات بالا را با من به اشتراک بگذارید تا بتوانم بهتر کمک کنم.


putty: 
***********    => https://api.voopee.co/api/docs
cd /home/<USER>/project/word-maker



rm -rf dist
rm -rf src
unzip word-maker-7.zip 

yarn update-database

فکر کنم از این به بعد باید بعد از اینکه فایل های src و dist رو آپلود کردی این دستورو اجرا کنی 
sudo kill -9 <PID>
node /home/<USER>/project/word-maker/dist/src/main.js &
بعد از این مرحله یه کم صبر کن تا سایت بالا بیاد . اولش خطا میده هنوز 

PID رو باید با این دستور پیدا کنی 
sudo lsof -i :3002
تو این لیست باید دنبال 3002 بگردی . آخری هست .

sudo kill -9 990757

رمز عبور کاربر root
JGDHoiDQYR