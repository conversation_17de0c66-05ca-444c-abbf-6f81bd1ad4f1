import { Column, <PERSON><PERSON><PERSON>, Join<PERSON><PERSON>um<PERSON>, OneToOne, ManyToOne } from 'typeorm';
import { AbstractEntity } from './abstract-entity';
import { users } from './users.entity';
import { province_cities } from './province-cities.entity';
import { EntityTypeEnum } from '../../enums/user.enums';

@Entity()
export class affiliate_info extends AbstractEntity {
  @Column({
    type: 'enum',
    enum: EntityTypeEnum,
  })
  entity_type: EntityTypeEnum;

  @Column({ nullable: true })
  registration_code: string;

  @Column({ nullable: true })
  corporation_name: string;

  @Column()
  province_id: number;

  @Column()
  city_id: number;

  @Column()
  address: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: true })
  national_code: string;

  @Column({ nullable: true })
  landline_phone: string;

  @Column()
  postal_code: string;

  @Column()
  user_id: number;

  @OneToOne(() => users, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'user_id' })
  user: users;

  @ManyToOne(() => province_cities)
  @JoinColumn({ name: 'province_id' })
  province: province_cities;

  @ManyToOne(() => province_cities)
  @JoinColumn({ name: 'city_id' })
  city: province_cities;
}
