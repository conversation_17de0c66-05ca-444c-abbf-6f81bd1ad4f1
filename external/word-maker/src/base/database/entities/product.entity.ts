import { Column, Entity, OneToMany } from 'typeorm';
import { AbstractEntity } from './abstract-entity';
import { AccessLevelEnum } from '../../enums/access-levels.enum';
import { purchases } from './purchase.entity';
import { payments } from './payments.entity';

@Entity()
export class products extends AbstractEntity {
  @Column()
  price: number;

  @Column({ nullable: true })
  currency: string;

  @Column({ nullable: true })
  sib_app_id: string;

  @Column()
  title: string;

  @Column({ nullable: true })
  description: string;

  @Column()
  accessLevel: AccessLevelEnum;

  @Column('simple-array', { nullable: true })
  restrictedAccessLevels: AccessLevelEnum[];

  @OneToMany(() => purchases, (purchase) => purchase.product)
  purchases: purchases[];

  @OneToMany(() => payments, (payment) => payment.product)
  payments: payments[];
}
