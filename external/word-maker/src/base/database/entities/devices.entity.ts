import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne } from 'typeorm';
import { AbstractEntity } from './abstract-entity';
import { users } from './users.entity';

@Entity()
export class devices extends AbstractEntity {
  @Column()
  device_id: string;

  @Column()
  refresh_token: string;

  @ManyToOne(() => users, (user) => user.devices)
  @JoinColumn({ name: 'user_id' })
  user: users;

  @Column()
  user_id: number;
}
