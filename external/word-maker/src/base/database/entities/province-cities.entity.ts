import { Column, Entity, ManyToOne, OneToMany, <PERSON><PERSON>C<PERSON>um<PERSON> } from 'typeorm';
import { AbstractEntity } from './abstract-entity';

@Entity('province_cities')
export class province_cities extends AbstractEntity {
  @Column({ type: 'bigint', default: 1 })
  user_id: number;

  @Column({ type: 'integer', default: 0 })
  parent: number;

  @Column({ type: 'varchar', length: 100 })
  title: string;

  @Column({ type: 'smallint', default: 1 })
  sort: number;

  @Column({ type: 'timestamp', nullable: true })
  deleted_at: Date;

  // Self-referencing relationship for provinces and cities
  @ManyToOne(() => province_cities, (provinceCity) => provinceCity.children, {
    nullable: true,
  })
  @JoinColumn({ name: 'parent' })
  parentProvince: province_cities;

  @OneToMany(
    () => province_cities,
    (provinceCity) => provinceCity.parentProvince,
  )
  children: province_cities[];
}
