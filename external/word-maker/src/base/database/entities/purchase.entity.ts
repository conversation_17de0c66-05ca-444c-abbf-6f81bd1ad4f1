import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, Unique } from 'typeorm';
import { AbstractEntity } from './abstract-entity';
import { PurchaseStateEnum } from '../../enums/purchase-state.enum';
import { users } from './users.entity';
import { products } from './product.entity';
import { payments } from './payments.entity';

@Entity()
@Unique(['token'])
export class purchases extends AbstractEntity {
  @Column({ nullable: true })
  orderId: string;

  @Column({ nullable: true })
  token: string;

  @Column({ nullable: true })
  consumptionState: string;

  @Column({ nullable: true })
  payload: string;

  @Column({ nullable: true })
  packageName: string;

  @Column()
  state: PurchaseStateEnum;

  @Column()
  time: Date;

  @Column({ nullable: true })
  originalJson: string;

  @Column({ nullable: true })
  dataSignature: string;

  @ManyToOne(() => users, (user) => user.purchases)
  @JoinColumn({ name: 'user_id' })
  user: users;

  @Column()
  user_id: number;

  @ManyToOne(() => products, (product) => product.purchases)
  @JoinColumn({ name: 'product_id' })
  product: products;

  @Column()
  product_id: number;

  @ManyToOne(() => payments, (payment) => payment.purchases)
  @JoinColumn({ name: 'payment_id' })
  payment: payments;

  @Column({ nullable: true })
  payment_id: number;
}
