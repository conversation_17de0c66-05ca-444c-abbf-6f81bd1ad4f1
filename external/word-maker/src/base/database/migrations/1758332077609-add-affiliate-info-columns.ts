import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddAffiliateInfoColumns1758332077609
  implements MigrationInterface
{
  name = 'AddAffiliateInfoColumns1758332077609';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "affiliate_info"
      ADD COLUMN "national_code" character varying,
      ADD COLUMN "landline_phone" character varying,
      ADD COLUMN "postal_code" character varying NOT NULL DEFAULT '00000'
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      ALTER TABLE "affiliate_info"
      DROP COLUMN "national_code",
      DROP COLUMN "landline_phone",
      DROP COLUMN "postal_code"
    `);
  }
}
