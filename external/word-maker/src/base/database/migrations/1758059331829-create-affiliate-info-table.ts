import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateAffiliateInfoTable1758059331829
  implements MigrationInterface
{
  name = 'CreateAffiliateInfoTable1758059331829';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      CREATE TABLE "affiliate_info" (
        "id" SERIAL NOT NULL,
        "created_at" TIMESTAMP NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP DEFAULT now(),
        "entity_type" character varying NOT NULL,
        "registration_code" character varying,
        "corporation_name" character varying,
        "province_id" integer NOT NULL,
        "city_id" integer NOT NULL,
        "address" character varying NOT NULL,
        "description" text,
        "user_id" integer NOT NULL,
        CONSTRAINT "PK_affiliate_info_id" PRIMARY KEY ("id")
      )
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_info" 
      ADD CONSTRAINT "FK_affiliate_info_user_id" 
      FOREIGN KEY ("user_id") REFERENCES "users"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_info" 
      ADD CONSTRAINT "FK_affiliate_info_province_id" 
      FOREIGN KEY ("province_id") REFERENCES "province_cities"("id") 
      ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "affiliate_info" 
      ADD CONSTRAINT "FK_affiliate_info_city_id" 
      FOREIGN KEY ("city_id") REFERENCES "province_cities"("id") 
      ON DELETE NO ACTION ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_affiliate_info_user_id" 
      ON "affiliate_info" ("user_id")
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP INDEX "IDX_affiliate_info_user_id"`);
    await queryRunner.query(
      `ALTER TABLE "affiliate_info" DROP CONSTRAINT "FK_affiliate_info_city_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "affiliate_info" DROP CONSTRAINT "FK_affiliate_info_province_id"`,
    );
    await queryRunner.query(
      `ALTER TABLE "affiliate_info" DROP CONSTRAINT "FK_affiliate_info_user_id"`,
    );
    await queryRunner.query(`DROP TABLE "affiliate_info"`);
  }
}
