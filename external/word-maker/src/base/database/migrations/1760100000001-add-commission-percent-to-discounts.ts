import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddCommissionPercentToDiscounts1760100000001
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    // Add nullable commission_percent column for affiliate/referral commission percentage (0-100)
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD "commission_percent" integer`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP COLUMN "commission_percent"`,
    );
  }
}
