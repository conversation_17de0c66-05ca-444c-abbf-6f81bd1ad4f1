import { MigrationInterface, QueryRunner } from 'typeorm';

export class NullableDiscountId1726306710793 implements MigrationInterface {
  name = 'NullableDiscountId1726306710793';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "payments" DROP CONSTRAINT "FK_ec2238cc2210c8e3e88aae6ac16"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payments" ALTER COLUMN "discount_id" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "payments" ADD CONSTRAINT "FK_ec2238cc2210c8e3e88aae6ac16" FOREIGN KEY ("discount_id") REFERENCES "discounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "payments" DROP CONSTRAINT "FK_ec2238cc2210c8e3e88aae6ac16"`,
    );
    await queryRunner.query(
      `ALTER TABLE "payments" ALTER COLUMN "discount_id" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "payments" ADD CONSTRAINT "FK_ec2238cc2210c8e3e88aae6ac16" FOREIGN KEY ("discount_id") REFERENCES "discounts"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }
}
