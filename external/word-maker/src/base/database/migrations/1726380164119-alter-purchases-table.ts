import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterPurchasesTable1726380164119 implements MigrationInterface {
  name = 'AlterPurchasesTable1726380164119';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "purchases" ADD "payment_id" integer`);
    await queryRunner.query(
      `ALTER TABLE "purchases" ALTER COLUMN "orderId" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" ALTER COLUMN "token" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" ALTER COLUMN "consumptionState" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" ALTER COLUMN "payload" DROP NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" ADD CONSTRAINT "FK_dbc56ca84c4d1700d120d74b837" FOREIGN KEY ("payment_id") REFERENCES "payments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchases" DROP CONSTRAINT "FK_dbc56ca84c4d1700d120d74b837"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" ALTER COLUMN "payload" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" ALTER COLUMN "consumptionState" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" ALTER COLUMN "token" SET NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" ALTER COLUMN "orderId" SET NOT NULL`,
    );
    await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "payment_id"`);
  }
}
