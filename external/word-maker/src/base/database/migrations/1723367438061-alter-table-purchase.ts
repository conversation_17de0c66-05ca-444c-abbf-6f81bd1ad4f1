import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterTablePurchase1723367438061 implements MigrationInterface {
  name = 'AlterTablePurchase1723367438061';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchases" ADD "consumptionState" character varying NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" ADD CONSTRAINT "UQ_00ae58d36fe99152ac93b0581f5" UNIQUE ("token")`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "purchases" DROP CONSTRAINT "UQ_00ae58d36fe99152ac93b0581f5"`,
    );
    await queryRunner.query(
      `ALTER TABLE "purchases" DROP COLUMN "consumptionState"`,
    );
  }
}
