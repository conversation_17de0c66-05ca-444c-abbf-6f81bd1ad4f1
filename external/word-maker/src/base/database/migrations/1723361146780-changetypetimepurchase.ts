import { MigrationInterface, QueryRunner } from 'typeorm';

export class Changetypetimepurchase1723361146780 implements MigrationInterface {
  name = 'Changetypetimepurchase1723361146780';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "time"`);
    await queryRunner.query(
      `ALTER TABLE "purchases" ADD "time" TIMESTAMP NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "purchases" DROP COLUMN "time"`);
    await queryRunner.query(
      `ALTER TABLE "purchases" ADD "time" integer NOT NULL`,
    );
  }
}
