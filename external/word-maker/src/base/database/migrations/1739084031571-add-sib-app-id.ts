import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddSibAppId1739084031571 implements MigrationInterface {
  name = 'AddSibAppId1739084031571';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "products" ADD "sib_app_id" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "sib_app_id"`);
  }
}
