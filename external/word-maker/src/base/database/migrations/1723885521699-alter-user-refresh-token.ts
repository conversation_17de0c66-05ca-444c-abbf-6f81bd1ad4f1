import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterUserRefreshToken1723885521699 implements MigrationInterface {
  name = 'AlterUserRefreshToken1723885521699';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" ADD "refresh_token" character varying`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "refresh_token"`);
  }
}
