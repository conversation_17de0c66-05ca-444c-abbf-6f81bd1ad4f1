import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterProductUserTable1723966508119 implements MigrationInterface {
  name = 'AlterProductUserTable1723966508119';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "products" ADD "currency" character varying`,
    );
    await queryRunner.query(
      `ALTER TYPE "public"."users_gender_enum" RENAME TO "users_gender_enum_old"`,
    );
    await queryRunner.query(
      `CREATE TYPE "public"."users_gender_enum" AS ENUM('MALE', 'FEMALE')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "gender" DROP DEFAULT`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "gender" TYPE "public"."users_gender_enum" USING "gender"::"text"::"public"."users_gender_enum"`,
    );
    await queryRunner.query(`DROP TYPE "public"."users_gender_enum_old"`);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."users_gender_enum_old" AS ENUM('MALE', 'FEMALE', 'OTHER')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "gender" TYPE "public"."users_gender_enum_old" USING "gender"::"text"::"public"."users_gender_enum_old"`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ALTER COLUMN "gender" SET DEFAULT 'OTHER'`,
    );
    await queryRunner.query(`DROP TYPE "public"."users_gender_enum"`);
    await queryRunner.query(
      `ALTER TYPE "public"."users_gender_enum_old" RENAME TO "users_gender_enum"`,
    );
    await queryRunner.query(`ALTER TABLE "products" DROP COLUMN "currency"`);
  }
}
