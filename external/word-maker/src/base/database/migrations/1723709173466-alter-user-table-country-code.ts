import { MigrationInterface, QueryRunner } from 'typeorm';

export class AlterUserTableCountryCode1723709173466
  implements MigrationInterface
{
  name = 'AlterUserTableCountryCode1723709173466';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "users" ADD "country_code" character varying NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "country_code"`);
  }
}
