import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddPaymentIdToDiscounts1757161895983
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "discounts" ADD "payment_id" integer`);
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD CONSTRAINT "FK_4c88e956195bba85977da21b8f4" FOREIGN KEY ("payment_id") REFERENCES "payments"("id") ON DELETE NO ACTION ON UPDATE NO ACTION`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP CONSTRAINT "FK_4c88e956195bba85977da21b8f4"`,
    );
    await queryRunner.query(`ALTER TABLE "discounts" DROP COLUMN "payment_id"`);
  }
}
