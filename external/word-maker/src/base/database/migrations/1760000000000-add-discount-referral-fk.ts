import { MigrationInterface, QueryRunner, TableFore<PERSON><PERSON><PERSON> } from 'typeorm';

export class AddDiscountReferralFk1760000000000 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    // First, set any invalid referral_id values to NULL
    await queryRunner.query(`
      UPDATE discounts
      SET referral_id = NULL
      WHERE referral_id IS NOT NULL
      AND referral_id NOT IN (SELECT id FROM affiliate_info)
    `);

    // Then create the foreign key constraint
    await queryRunner.createForeignKey(
      'discounts',
      new TableForeignKey({
        columnNames: ['referral_id'],
        referencedColumnNames: ['id'],
        referencedTableName: 'affiliate_info',
        onDelete: 'SET NULL',
        name: 'FK_discounts_referral_id',
      }),
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropForeignKey('discounts', 'FK_discounts_referral_id');
  }
}
