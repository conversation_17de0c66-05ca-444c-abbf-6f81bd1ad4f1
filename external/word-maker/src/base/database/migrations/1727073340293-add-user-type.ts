import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserType1727073340293 implements MigrationInterface {
  name = 'AddUserType1727073340293';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `CREATE TYPE "public"."users_usertype_enum" AS ENUM('GENERAL', 'MASTER')`,
    );
    await queryRunner.query(
      `ALTER TABLE "users" ADD "userType" "public"."users_usertype_enum" NOT NULL DEFAULT 'GENERAL'`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "userType"`);
    await queryRunner.query(`DROP TYPE "public"."users_usertype_enum"`);
  }
}
