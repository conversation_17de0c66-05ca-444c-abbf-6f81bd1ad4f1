import { MigrationInterface, QueryRunner } from 'typeorm';

export class AddUserRoles1739084031572 implements MigrationInterface {
  name = 'AddUserRoles1739084031572';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create the roles enum type
    await queryRunner.query(
      `CREATE TYPE "public"."users_roles_enum" AS ENUM('ADMIN', 'AFFILIATE', 'USER')`,
    );

    // Add the roles column with default value
    await queryRunner.query(
      `ALTER TABLE "users" ADD "roles" "public"."users_roles_enum"[] NOT NULL DEFAULT '{USER}'`,
    );

    // Set admin users (based on common admin phone numbers) to ADMIN role
    // Note: In production, you should update this query to match your actual admin phone numbers
    await queryRunner.query(
      `UPDATE "users" SET "roles" = '{ADMIN}' WHERE "phone" IN ('9155528224','9100400197','9113331643','9396186896','9368014847')`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "users" DROP COLUMN "roles"`);
    await queryRunner.query(`DROP TYPE "public"."users_roles_enum"`);
  }
}
