import { MigrationInterface, QueryRunner } from 'typeorm';

export class Remove2ColumnsDiscounts1725773947331
  implements MigrationInterface
{
  name = 'Remove2ColumnsDiscounts1725773947331';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`ALTER TABLE "discounts" DROP COLUMN "count"`);
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP COLUMN "remain_count"`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD "remain_count" integer NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD "count" integer NOT NULL`,
    );
  }
}
