import { MigrationInterface, QueryRunner } from 'typeorm';

export class Add2ColumnDiscount1725606700251 implements MigrationInterface {
  name = 'Add2ColumnDiscount1725606700251';

  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD "discount_percent" integer NOT NULL`,
    );
    await queryRunner.query(
      `ALTER TABLE "discounts" ADD "remain_count" integer NOT NULL`,
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP COLUMN "remain_count"`,
    );
    await queryRunner.query(
      `ALTER TABLE "discounts" DROP COLUMN "discount_percent"`,
    );
  }
}
