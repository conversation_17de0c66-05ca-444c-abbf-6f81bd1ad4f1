import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';
import moment from 'moment';

@Injectable()
export class DateTransformPipe implements PipeTransform {
  transform(value: any) {
    if (!value) {
      return value;
    }

    const date = moment(value, 'YYYY-MM-DD', true);
    if (!date.isValid()) {
      throw new BadRequestException(
        'Invalid date format. Expected format: YYYY-MM-DD',
      );
    }

    return date.endOf('day').toDate(); // اضافه کردن زمان 23:59:59
  }
}
