import { PipeTransform, Injectable, BadRequestException } from '@nestjs/common';

@Injectable()
export class ExpireDateValidationPipe implements PipeTransform {
  transform(value: any) {
    const expireDate = new Date(value.expire_date);
    const today = new Date();

    if (expireDate < today) {
      throw new BadRequestException('Expire date cannot be in the past.');
    }

    return value;
  }
}
