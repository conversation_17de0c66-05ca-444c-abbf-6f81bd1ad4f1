import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
  UnauthorizedException,
} from '@nestjs/common';
import { Observable } from 'rxjs';

@Injectable()
export class PhoneCheckInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const user = request.user;
    if (user.username !== '9155528224' && user.username !== '9113331643') {
      throw new UnauthorizedException('شما مجاز به انجام این عملیات نیستید.');
    }

    return next.handle();
  }
}
