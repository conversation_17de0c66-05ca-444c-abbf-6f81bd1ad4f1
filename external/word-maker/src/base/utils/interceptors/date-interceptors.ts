import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

@Injectable()
export class TransformDateInterceptor implements NestInterceptor {
  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    return next.handle().pipe(
      map((data) => {
        if (data.expire_date) {
          data.expire_date = new Date(
            data.expire_date.getTime() -
              data.expire_date.getTimezoneOffset() * 60000,
          );
        }
        return data;
      }),
    );
  }
}
