import { IsNumber, IsString } from 'class-validator';
import { Mapped } from '../utils/mapper/mapper.decorator';
import { ApiProperty } from '@nestjs/swagger';

export class HttpResponseDto {
  @IsNumber()
  @Mapped()
  @ApiProperty({ example: 200, description: 'وضعیت پاسخ HTTP' })
  status: number;

  @IsString()
  @Mapped()
  @ApiProperty({ example: 'عملیات موفقیتآمیز بود', description: 'پیام پاسخ' })
  message: string;

  @Mapped()
  @ApiProperty({ example: {}, description: 'دادههای پاسخ' })
  data?: any;
}
