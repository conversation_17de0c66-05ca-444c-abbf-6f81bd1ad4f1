import { Processor, Process } from '@nestjs/bull';
import { Job } from 'bull';
import * as nodemailer from 'nodemailer';

@Processor('email')
export class EmailProcessor {
  private transporter;

  constructor() {
    console.log('constructor email 1');
    this.transporter = nodemailer.createTransport({
      host: 'smtp.gmail.com',
      port: 587,
      secure: false, // true برای پورت 465، false برای پورتهای دیگر
      auth: {
        user: '<EMAIL>',
        pass: 'dqqk lmgp htkb dgme',
      },
      tls: {
        rejectUnauthorized: false,
      },
      connectionTimeout: 60000, // 60 ثانیه
      socketTimeout: 60000, // 60 ثانیه
    });
    console.log('constructor email 2');
  }

  @Process('sendEmail')
  async handleSendEmail(job: Job) {
    const { to, subject, text } = job.data;

    const mailOptions = {
      from: '<EMAIL>',
      to,
      subject,
      text,
    };

    try {
      await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully');
    } catch (error) {
      console.error('Error sending email:', error);
    }
  }
}
