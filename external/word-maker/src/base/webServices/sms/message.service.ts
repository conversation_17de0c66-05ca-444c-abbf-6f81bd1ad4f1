import { Injectable } from '@nestjs/common';
import { soap } from 'strong-soap';

@Injectable()
export class MessageService {
  async sendSMS(to: string, message: string): Promise<any> {
    return new Promise((resolve, reject) => {
      soap.createClient(process.env.SMS_WSDL, (err, client) => {
        if (err) {
          return reject(err);
        }

        const args = {
          username: process.env.SMS_USERNAME,
          password: process.env.SMS_PASSWORD,
          to,
          from: process.env.from,
          message: `کد تایید شما جهت ورود ${message} می باشد \n i/ch2IL7Z+i`,
        };
        client.send(args, (err, result) => {
          if (err) {
            return reject(err);
          }
          resolve(result);
        });
      });
    });
  }
}
