import { Modu<PERSON> } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { BazarModule } from '../base/webServices/bazar/bazar.module';
import { LogModule } from '../base/logger/log.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProductService } from './product.service';
import { ProductController } from './product.controller';
import { ProductRepository } from './product.repository';
import { products } from '../base/database/entities/product.entity';
import { MapperService } from '../base/utils/mapper/mapper.service';
import { TokenService } from '../token/token.service';
@Module({
  imports: [
    HttpModule,
    BazarModule,
    LogModule,
    TypeOrmModule.forFeature([products]),
  ],
  providers: [ProductService, ProductRepository, MapperService, TokenService],
  controllers: [ProductController],
  exports: [ProductService, ProductRepository],
})
export class ProductModule {}
