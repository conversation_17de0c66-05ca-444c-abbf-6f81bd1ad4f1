import {
  Controller,
  Get,
  UseGuards,
  UseInterceptors,
  Request,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { ProductService } from './product.service';
import { ProductOutputDto } from './product.dto';
import { MapInterceptor } from '../base/utils/mapper/mapper.interceptor';
import { JwtOptionalGuard } from '../auth/optional.auth.gaurd';

@Controller('products')
@ApiTags('products')
@UseGuards(JwtOptionalGuard)
@ApiBearerAuth()
export class ProductController {
  constructor(private productService: ProductService) {}
  @Get('/all')
  @ApiResponse({
    status: 200,
    description: 'لیست محصولات',
    type: ProductOutputDto,
  })
  @UseInterceptors(MapInterceptor(ProductOutputDto))
  async products(@Request() req): Promise<ProductOutputDto[]> {
    return await this.productService.getProducts(req.user?.username);
  }
}
