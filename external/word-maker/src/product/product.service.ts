import { Injectable } from '@nestjs/common';
import { ProductRepository } from './product.repository';
@Injectable()
export class ProductService {
  constructor(private readonly productRepository: ProductRepository) {}

  async getProducts(username?: string) {
    const products = await this.productRepository.get();
    const whiteUsers = (process.env.ADMIN_USERNAMES || '')
      .split(',')
      .map((u) => u.trim());

    if (!whiteUsers.includes(username)) {
      return products.filter((item) => item.id !== 3);
    }
    return products;
  }
  async getProductById(id: number) {
    return await this.productRepository.getById(id);
  }
}
