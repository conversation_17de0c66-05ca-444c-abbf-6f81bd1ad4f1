import { Mapped } from '../base/utils/mapper/mapper.decorator';
import { MappedWithEntity } from '../base/utils/mapper/mapper.types';
import { products } from '../base/database/entities/product.entity';
import { AccessLevelEnum } from '../base/enums/access-levels.enum';
import { ApiProperty } from '@nestjs/swagger';

export class ProductOutputDto implements MappedWithEntity<products> {
  @ApiProperty()
  @Mapped()
  id: number;
  @ApiProperty()
  @Mapped()
  price?: number;
  @ApiProperty()
  @Mapped()
  title?: string;
  @ApiProperty()
  @Mapped()
  sib_app_id?: string;
  @ApiProperty()
  @Mapped()
  currency?: string;
  @ApiProperty()
  @Mapped()
  description?: string;
  @ApiProperty()
  @Mapped()
  accessLevel: AccessLevelEnum;
  @ApiProperty()
  @Mapped()
  restrictedAccessLevels: AccessLevelEnum[];
}
