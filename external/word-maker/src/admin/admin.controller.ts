import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  Res,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { PhoneCheckInterceptor } from '../base/utils/interceptors/user-interceptors';
import { AuthGuard } from '../auth/auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { Roles } from '../auth/roles.decorator';
import { RoleEnum } from '../base/enums/user.enums';
import { MapInterceptor } from '../base/utils/mapper/mapper.interceptor';
import { UserOutputDto } from '../user/users.dto';
import { UpdateUserTypeInputDto, UserSearchDto } from './admin.dto';
import { AdminService } from './admin.service';
import { Response } from 'express';
import {
  UserRegistrationOutputDto,
  UserRegistrationInputDto,
} from '../auth/auth.dto';
import { UserTypeEnum } from '../base/enums/user.enums';
import { AuthService } from '../auth/auth.service';
import { HttpResponseDto } from '../base/dto/general.dto';

@Controller('admin')
@ApiTags('admin')
@ApiBearerAuth()
@UseGuards(AuthGuard, RolesGuard)
@Roles(RoleEnum.ADMIN)
export class AdminController {
  constructor(
    private adminService: AdminService,
    private authService: AuthService,
  ) {}
  @Get('/all-user-excel')
  @ApiResponse({
    status: 200,
    description: 'لیست کاربران',
  })
  async getUsersExcel(
    @Query() discountSearchDto: UserSearchDto,
    @Res() res: Response,
  ): Promise<void> {
    return await this.adminService.getUsersExcel(discountSearchDto, res);
  }

  @Get('/all-user')
  @ApiResponse({
    status: 200,
    description: 'لیست کاربران',
    type: UserOutputDto,
  })
  @UseInterceptors(MapInterceptor(UserOutputDto))
  async discounts(
    @Query() discountSearchDto: UserSearchDto,
  ): Promise<UserOutputDto[]> {
    return await this.adminService.getUsers(discountSearchDto);
  }

  @Post('/register-master-user')
  @ApiResponse({
    status: 200,
    type: UserRegistrationOutputDto,
  })
  @UseInterceptors(MapInterceptor(UserRegistrationOutputDto))
  async register(
    @Body() userRegistrationInputDto: UserRegistrationInputDto,
  ): Promise<UserRegistrationOutputDto> {
    return await this.authService.register({
      ...userRegistrationInputDto,
      userType: UserTypeEnum.MASTER,
    });
  }
  @Post('/update-user-type')
  @UseInterceptors(MapInterceptor(HttpResponseDto))
  @ApiResponse({
    status: 200,
    description: 'اطلاعات کاربر با موفقیت ویرایش شد',
    type: HttpResponseDto,
  })
  async updateUserType(
    @Body() updateUserTypeInputDto: UpdateUserTypeInputDto,
  ): Promise<HttpResponseDto> {
    return await this.adminService.updateUserType(updateUserTypeInputDto);
  }
}
