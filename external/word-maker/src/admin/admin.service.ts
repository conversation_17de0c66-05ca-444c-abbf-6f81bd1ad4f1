import { Injectable, NotFoundException } from '@nestjs/common';
import { UsersRepository } from '../user/user.repository';
import { UpdateUserTypeInputDto, UserSearchDto } from './admin.dto';
import { Workbook } from 'exceljs';
import { Response } from 'express';
import { HttpResponseDto } from '../base/dto/general.dto';
import { convertToHttpResponseDto } from '../base/utils/convert-info';
@Injectable()
export class AdminService {
  constructor(private readonly userRepository: UsersRepository) {}

  async getUsers(userSearchDto: UserSearchDto) {
    return await this.userRepository.get(userSearchDto);
  }
  async getUsersExcel(userSearchDto: UserSearchDto, res: Response) {
    const users = await this.userRepository.get(userSearchDto);

    const workbook = new Workbook();
    const worksheet = workbook.addWorksheet('Users');

    worksheet.columns = [
      { header: 'ID', key: 'id', width: 10 },
      { header: 'Name', key: 'name', width: 30 },
      { header: 'Family', key: 'family', width: 30 },
      { header: 'Country_code', key: 'country_code', width: 30 },
      { header: 'Phone', key: 'phone', width: 30 },
      { header: 'Email', key: 'email', width: 30 },
      { header: 'Country_name', key: 'country_name', width: 30 },
      { header: 'Birth_date', key: 'birth_date', width: 30 },
      { header: 'User_Type', key: 'userType', width: 30 },
    ];

    users.forEach((user) => {
      worksheet.addRow(user);
    });

    res.setHeader(
      'Content-Type',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    );
    res.setHeader('Content-Disposition', 'attachment; filename=users.xlsx');

    await workbook.xlsx.write(res);
    res.end();
  }

  async updateUserType(
    updateUserTypeInputDto: UpdateUserTypeInputDto,
  ): Promise<HttpResponseDto> {
    const { phone, country_code, userType } = updateUserTypeInputDto;
    const found = await this.userRepository.checkExistByPhone(
      phone,
      country_code,
    );
    if (!found) throw new NotFoundException('شماره تلفن وارد شده یافت نشد');
    return convertToHttpResponseDto(
      await this.userRepository.updateUserTypeInfo(found.id, userType),
    );
  }
}
