import { forwardRef, Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { MapperService } from '../base/utils/mapper/mapper.service';
import { TokenService } from '../token/token.service';
import { users } from '../base/database/entities/users.entity';
import { AdminService } from './admin.service';
import { UsersRepository } from '../user/user.repository';
import { AdminController } from './admin.controller';
import { devices } from '../base/database/entities/devices.entity';
import { AuthModule } from '../auth/auth.module';
@Module({
  imports: [
    TypeOrmModule.forFeature([users]),
    TypeOrmModule.forFeature([devices]),
    forwardRef(() => AuthModule), // استفاده از forwardRef,
  ],
  providers: [AdminService, UsersRepository, MapperService, TokenService],
  controllers: [AdminController],
  exports: [AdminService],
})
export class AdminModule {}
