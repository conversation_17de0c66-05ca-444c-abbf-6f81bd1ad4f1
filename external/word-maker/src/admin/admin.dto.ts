import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  Matches,
} from 'class-validator';
import { Mapped } from '../base/utils/mapper/mapper.decorator';
import { UserTypeEnum } from '../base/enums/user.enums';

export class UserSearchDto {
  @ApiPropertyOptional()
  @IsOptional()
  @Mapped()
  phone?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Mapped()
  email?: string;

  @IsEnum(UserTypeEnum, {
    message: 'نوع کاربر باید یکی از مقادیر GENERAL یا MASTER باشد',
  })
  @ApiPropertyOptional()
  @IsOptional()
  userType?: UserTypeEnum;
}

export class UserRegistrationMasterInputDto {
  @IsNotEmpty({ message: '  نام کشور نمی تواند خالی باشد' })
  @ApiProperty({ example: 'IR', description: 'نام کشور' })
  country_name: string;

  @Matches(/^\+\d{1,3}$/, {
    message: 'کد کشور باید با + شروع شود و شامل 1 تا 3 عدد باشد',
  })
  @IsNotEmpty({ message: '  کد کشور نمی تواند خالی باشد' })
  @ApiProperty({ example: '+98', description: 'کد کشور' })
  country_code: string;

  @Matches(/^\d{9,10}$/, {
    message: 'شماره تلفن معتبر نیست. باید 9 یا 10 رقمی باشد',
  })
  @IsNotEmpty({ message: ' شماره تلفن نمیتواند خالی باشد' })
  @ApiProperty({ example: '9121111111', description: 'شماره تلفن ' })
  phone: string;

  @IsOptional()
  @ApiProperty({ example: 'email', description: 'ایمیل' })
  @IsEmail({}, { message: 'ایمیل معتبر نیست' })
  email: string;

  @IsEnum(UserTypeEnum, {
    message: 'نوع کاربر باید یکی از مقادیر GENERAL یا MASTER باشد',
  })
  @ApiProperty({ example: 'FEMALE', description: 'نوع کاربر' })
  @IsOptional()
  userType: UserTypeEnum;
}

export class UpdateUserTypeInputDto {
  @IsNotEmpty({ message: '  نام کشور نمی تواند خالی باشد' })
  @ApiProperty({ example: 'IR', description: 'نام کشور' })
  country_name: string;

  @Matches(/^\+\d{1,3}$/, {
    message: 'کد کشور باید با + شروع شود و شامل 1 تا 3 عدد باشد',
  })
  @IsNotEmpty({ message: '  کد کشور نمی تواند خالی باشد' })
  @ApiProperty({ example: '+98', description: 'کد کشور' })
  country_code: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Mapped()
  phone?: string;

  @IsEnum(UserTypeEnum, {
    message: 'نوع کاربر باید یکی از مقادیر GENERAL یا MASTER باشد',
  })
  @ApiPropertyOptional()
  @IsOptional()
  userType?: UserTypeEnum;
}
