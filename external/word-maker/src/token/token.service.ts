import { Injectable, Inject } from '@nestjs/common';
import { DEFAULT_REDIS, RedisService } from '@liaoliaots/nestjs-redis';
import { Redis } from 'ioredis';

@Injectable()
export class TokenService {
  private client: Redis;

  constructor(private readonly redisService: RedisService) {
    this.client = this.redisService.getOrThrow(DEFAULT_REDIS);
  }

  async addToBlacklist(token: string) {
    await this.client.set(token, 'blacklisted');
  }

  async isBlacklisted(token: string): Promise<boolean> {
    const result = await this.client.get(token);
    return result === 'blacklisted';
  }
}
