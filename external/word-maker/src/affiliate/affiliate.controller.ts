import { Roles } from '../auth/roles.decorator';
import { RoleEnum } from '../base/enums/user.enums';
import {
  Controller,
  Get,
  Post,
  Put,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
  Req,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiBearerAuth,
  ApiResponse,
  ApiTags,
  ApiQuery,
  ApiParam,
  ApiOkResponse,
  ApiUnauthorizedResponse,
  ApiForbiddenResponse,
  ApiBadRequestResponse,
} from '@nestjs/swagger';
import { AffiliateService } from './affiliate.service';
import {
  CreateAffiliateDto,
  UpdateAffiliateDto,
  AffiliateResponseDto,
  AffiliateListResponseDto,
  SearchAffiliatesDto,
  AffiliateFiltersDto,
} from './affiliate.dto';
import { AffiliateAnalyticsQueryDto } from './dto/affiliate-analytics.query.dto';
import { AffiliateDashboardResponseDto } from './dto/affiliate-dashboard.response.dto';
import {
  AffiliateDiscountCodesResponseDto,
  AffiliateDiscountCodesSearchDto,
} from './dto/affiliate-discount-codes.response.dto';
import { AffiliateCommissionsResponseDto } from './dto/affiliate-commissions.response.dto';
import { AuthGuard } from '../auth/auth.guard';
import { RolesGuard } from '../auth/roles.guard';
import { EntityTypeEnum, UserStatusEnum } from '../base/enums/user.enums';
import { JwtService } from '@nestjs/jwt';

@Controller('affiliates')
@ApiTags('Affiliates')
export class AffiliateController {
  constructor(
    private readonly affiliateService: AffiliateService,
    private jwtService: JwtService,
  ) {}

  @Post()
  @ApiResponse({
    status: 201,
    description: 'همکار با موفقیت ثبت شد',
    type: AffiliateResponseDto,
  })
  @ApiResponse({
    status: 400,
    description: 'اطلاعات ارسالی نامعتبر است',
  })
  @ApiResponse({
    status: 409,
    description: 'کاربری با این شماره تلفن یا ایمیل قبلاً ثبت شده است',
  })
  async registerAffiliate(
    @Body() createAffiliateDto: CreateAffiliateDto,
    @Request() req,
  ): Promise<AffiliateResponseDto> {
    // Check if user is admin
    const [type, token] = req.headers.authorization?.split(' ') ?? [];
    const payload = await this.jwtService.verifyAsync(token, {
      secret: process.env.SECRET_KEY,
    });

    const isAdmin = payload?.roles?.includes(RoleEnum.ADMIN) || false;

    return this.affiliateService.registerAffiliate(createAffiliateDto, isAdmin);
  }

  @Get()
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @UseGuards(RolesGuard)
  @Roles(RoleEnum.ADMIN)
  @ApiQuery({
    name: 'page',
    required: false,
    description: 'شماره صفحه (پیش‌فرض: 1)',
    type: 'number',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    description: 'تعداد آیتم در هر صفحه (پیش‌فرض: 10)',
    type: 'number',
  })
  @ApiQuery({
    name: 'entity_type',
    required: false,
    description: 'نوع نهاد',
    enum: EntityTypeEnum,
  })
  @ApiQuery({
    name: 'status',
    required: false,
    description: 'وضعیت',
    enum: UserStatusEnum,
  })
  @ApiQuery({
    name: 'province_id',
    required: false,
    description: 'شناسه استان',
    type: 'number',
  })
  @ApiQuery({
    name: 'city_id',
    required: false,
    description: 'شناسه شهر',
    type: 'number',
  })
  @ApiQuery({
    name: 'search',
    required: false,
    description: 'عبارت جستجو',
    type: 'string',
  })
  @ApiResponse({
    status: 200,
    description: 'لیست همکاران با موفقیت بازیابی شد',
    type: AffiliateListResponseDto,
  })
  @ApiResponse({
    status: 403,
    description: 'شما مجاز به انجام این عملیات نیستید',
  })
  async listAffiliates(
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query() filters: AffiliateFiltersDto,
  ): Promise<AffiliateListResponseDto> {
    // Ensure positive values and reasonable limits
    const validPage = Math.max(1, page);
    const validLimit = Math.min(Math.max(1, limit), 100);

    return this.affiliateService.listAffiliates(validPage, validLimit, filters);
  }

  @Get('dashboard')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleEnum.AFFILIATE)
  @ApiBearerAuth()
  @ApiOkResponse({ type: AffiliateDashboardResponseDto })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @ApiBadRequestResponse({ description: 'Validation error' })
  async dashboard(@Req() req) {
    return await this.affiliateService.getDashboard(req.user);
  }

  @Get('discount-codes')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleEnum.AFFILIATE)
  @ApiBearerAuth()
  @ApiOkResponse({ type: AffiliateDiscountCodesResponseDto })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @ApiBadRequestResponse({ description: 'Validation error' })
  async getDiscountCodes(
    @Query() query: AffiliateDiscountCodesSearchDto,
    @Req() req,
  ) {
    return await this.affiliateService.getDiscountCodes(req.user, query);
  }

  @Get('commissions')
  @UseGuards(AuthGuard, RolesGuard)
  @Roles(RoleEnum.AFFILIATE)
  @ApiBearerAuth()
  @ApiOkResponse({ type: AffiliateCommissionsResponseDto })
  @ApiUnauthorizedResponse({ description: 'Unauthorized' })
  @ApiForbiddenResponse({ description: 'Forbidden' })
  @ApiBadRequestResponse({ description: 'Validation error' })
  async getCommissions(@Query() query: AffiliateAnalyticsQueryDto, @Req() req) {
    return await this.affiliateService.getCommissions(req.user, query);
  }

  @Get(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiParam({
    name: 'id',
    description: 'شناسه همکار',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'اطلاعات همکار با موفقیت بازیابی شد',
    type: AffiliateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'همکار مورد نظر یافت نشد',
  })
  async getAffiliate(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<AffiliateResponseDto> {
    return this.affiliateService.getAffiliate(id);
  }

  @Put(':id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiParam({
    name: 'id',
    description: 'شناسه همکار',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'اطلاعات همکار با موفقیت بروزرسانی شد',
    type: AffiliateResponseDto,
  })
  @ApiResponse({
    status: 404,
    description: 'همکار مورد نظر یافت نشد',
  })
  @ApiResponse({
    status: 400,
    description: 'اطلاعات ارسالی نامعتبر است',
  })
  @ApiResponse({
    status: 409,
    description: 'کاربری با این ایمیل قبلاً ثبت شده است',
  })
  async updateAffiliate(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAffiliateDto: UpdateAffiliateDto,
    @Request() req,
  ): Promise<AffiliateResponseDto> {
    const isAdmin = req.user?.roles?.includes(RoleEnum.ADMIN) || false;

    return this.affiliateService.updateAffiliate(
      id,
      updateAffiliateDto,
      isAdmin,
    );
  }
}
