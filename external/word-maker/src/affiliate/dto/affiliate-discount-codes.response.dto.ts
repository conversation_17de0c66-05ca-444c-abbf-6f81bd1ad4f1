import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PaginationMetaDto } from './paginiation.dto';
import { DiscountStateEnum } from 'src/base/database/entities/discounts.entity';
import { IsOptional } from 'class-validator';
import { AffiliateAnalyticsQueryDto } from './affiliate-analytics.query.dto';

export class DiscountCodeItemDto {
  @ApiProperty({
    description: 'Discount ID',
    example: 123,
  })
  discountId: number;

  @ApiProperty({
    description: 'Discount code',
    example: 'AFF123ABC',
  })
  code: string;

  @ApiProperty({
    description: 'Discount state',
    example: 'ACTIVE',
  })
  state: string;

  @ApiProperty({
    description: 'Commission percentage',
    example: 10,
  })
  commissionPercent: number;

  @ApiProperty({
    description: 'Expiration date in ISO string',
    nullable: true,
    example: '2024-12-31T23:59:59.999Z',
  })
  expireDate: string | null;

  @ApiProperty({
    description: 'Creation date in ISO string',
    example: '2023-01-15T10:30:00.000Z',
  })
  createdAt: string;
}

export class AffiliateDiscountCodesSearchDto extends AffiliateAnalyticsQueryDto {
  @ApiPropertyOptional({
    enum: DiscountStateEnum,
    example: 'ACTIVE',
    description: 'وضعیت کد تخفیف',
  })
  @IsOptional()
  state?: DiscountStateEnum;
}

export class AffiliateDiscountCodesResponseDto {
  @ApiProperty({
    description: 'Total number of discount codes created',
    example: 50,
  })
  totalDiscountCodes: number;

  @ApiProperty({
    description: 'Number of discount codes that have been sold/used',
    example: 25,
  })
  soldDiscountCodes: number;

  @ApiProperty({
    description: 'Total commission earned as string for precision',
    example: '225.50',
  })
  totalCommissionEarned: string;

  @ApiProperty({
    type: [DiscountCodeItemDto],
    description: 'Array of discount codes',
  })
  data: DiscountCodeItemDto[];

  @ApiProperty({ type: PaginationMetaDto })
  pagination: PaginationMetaDto;
}
