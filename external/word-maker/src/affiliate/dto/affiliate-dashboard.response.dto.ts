import { ApiExtraModels, ApiProperty } from '@nestjs/swagger';

export class SalesItemDto {
  @ApiProperty({
    description: 'Payment ID',
    example: 456,
  })
  paymentId: number;

  @ApiProperty({
    description: 'Discount ID',
    example: 123,
  })
  discountId: number;

  @ApiProperty({
    description: 'Discount code',
    example: 'AFF123ABC',
  })
  code: string;

  @ApiProperty({
    description: 'Original price',
    example: 100,
  })
  price: number;

  @ApiProperty({
    description: 'Commission percentage',
    example: 10,
  })
  commissionPercent: number;

  @ApiProperty({
    description: 'Commission amount earned',
    example: '10.00',
  })
  commissionAmount: string;

  @ApiProperty({
    description: 'Creation date in ISO string',
    example: '2023-01-20T14:45:00.000Z',
  })
  createdAt: string;
}

export class AffiliateDashboardResponseDto {
  @ApiProperty({
    description: 'Total number of discount codes created',
    example: 50,
  })
  totalDiscountCodes: number;

  @ApiProperty({
    description: 'Number of discount codes that have been sold/used',
    example: 25,
  })
  soldDiscountCodes: number;

  @ApiProperty({
    description: 'Total commission earned as string for precision',
    example: '225.50',
  })
  totalCommissionEarned: string;

  @ApiProperty({
    description: 'Total commission earned as string for precision',
    type: [SalesItemDto],
  })
  sales: SalesItemDto[];
}
