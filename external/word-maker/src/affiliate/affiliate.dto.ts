import { ApiProperty } from '@nestjs/swagger';
import { affiliate_info } from '../base/database/entities/affiliate-info.entity';
import { Mapped } from '../base/utils/mapper/mapper.decorator';
import { MappedWithEntity } from '../base/utils/mapper/mapper.types';
import {
  IsEnum,
  IsNotEmpty,
  IsOptional,
  IsString,
  IsNumber,
  ValidateIf,
  IsEmail,
  Matches,
} from 'class-validator';
import { EntityTypeEnum, UserStatusEnum } from '../base/enums/user.enums';
import { users } from 'src/base/database/entities/users.entity';

export class CreateAffiliateDto {
  @ApiProperty({
    example: 'John',
    description: 'نام (اجباری برای اشخاص حقیقی)',
  })
  @ValidateIf((o) => o.entity_type === EntityTypeEnum.PERSON)
  @IsNotEmpty({ message: 'نام برای اشخاص حقیقی اجباری است' })
  @IsString({ message: 'عب<PERSON><PERSON>ت جستجو باید رشته باشد' })
  name?: string;

  @ApiProperty({
    example: 'Doe',
    description: 'نام خانوادگی (اجباری برای اشخاص حقیقی)',
  })
  @ValidateIf((o) => o.entity_type === EntityTypeEnum.PERSON)
  @IsNotEmpty({ message: 'نام خانوادگی برای اشخاص حقیقی اجباری است' })
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  family?: string;

  @ApiProperty({
    example: '9123456789',
    description: 'شماره موبایل (اجباری)',
  })
  @IsNotEmpty({ message: 'شماره موبایل اجباری است' })
  @Matches(/^\d{9,10}$/, {
    message: 'شماره تلفن معتبر نیست. باید 9 یا 10 رقمی باشد',
  })
  phone: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'ایمیل (اختیاری)',
  })
  @IsOptional()
  @IsEmail({}, { message: 'ایمیل معتبر نیست' })
  email?: string;

  @ApiProperty({
    example: EntityTypeEnum.PERSON,
    description: 'نوع نهاد',
    enum: EntityTypeEnum,
  })
  @IsEnum(EntityTypeEnum, {
    message: 'نوع نهاد باید یکی از مقادیر PERSON یا CORPORATION باشد',
  })
  entity_type: EntityTypeEnum;

  @ApiProperty({
    example: '123456789',
    description: 'کد ثبت شرکت (اجباری برای اشخاص حقوقی)',
  })
  @ValidateIf((o) => o.entity_type === EntityTypeEnum.CORPORATION)
  @IsNotEmpty({ message: 'کد ثبت شرکت برای اشخاص حقوقی اجباری است' })
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  registration_code?: string;

  @ApiProperty({
    example: 'شرکت نمونه',
    description: 'نام شرکت (اجباری برای اشخاص حقوقی)',
  })
  @ValidateIf((o) => o.entity_type === EntityTypeEnum.CORPORATION)
  @IsNotEmpty({ message: 'نام شرکت برای اشخاص حقوقی اجباری است' })
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  corporation_name?: string;

  @ApiProperty({
    example: 8,
    description: 'شناسه استان (اجباری)',
  })
  @IsNotEmpty({ message: 'شناسه استان اجباری است' })
  @IsNumber({}, { message: 'شناسه استان باید عدد باشد' })
  province_id: number;

  @ApiProperty({
    example: 45,
    description: 'شناسه شهر (اجباری)',
  })
  @IsNotEmpty({ message: 'شناسه شهر اجباری است' })
  @IsNumber({}, { message: 'شناسه شهر باید عدد باشد' })
  city_id: number;

  @ApiProperty({
    example: 'خیابان ولیعصر، پلاک 123',
    description: 'آدرس (اجباری)',
  })
  @IsNotEmpty({ message: 'آدرس اجباری است' })
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  address: string;

  @ApiProperty({
    example: 'توضیحات اضافی',
    description: 'توضیحات (اختیاری)',
  })
  @IsOptional()
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  description?: string;

  @ApiProperty({
    example: UserStatusEnum.PENDING,
    description: 'وضعیت (فقط برای ادمین)',
    enum: UserStatusEnum,
  })
  @IsOptional()
  @IsEnum(UserStatusEnum, {
    message: 'وضعیت باید یکی از مقادیر معتبر باشد',
  })
  status?: UserStatusEnum;

  @ApiProperty({
    example: '1234567890',
    description: 'کد ملی (اجباری برای اشخاص حقیقی)',
  })
  @ValidateIf((o) => o.entity_type === EntityTypeEnum.PERSON)
  @IsNotEmpty({ message: 'کد ملی برای اشخاص حقیقی اجباری است' })
  @IsString({ message: 'کد ملی باید رشته باشد' })
  @Matches(/^\d{10}$/, {
    message: 'کد ملی معتبر نیست. باید 10 رقمی باشد',
  })
  national_code?: string;

  @ApiProperty({
    example: '02112345678',
    description: 'شماره تلفن ثابت (اختیاری)',
  })
  @IsOptional()
  @IsString({ message: 'شماره تلفن ثابت باید رشته باشد' })
  @Matches(/^\d{8,11}$/, {
    message: 'شماره تلفن ثابت معتبر نیست. باید 8 تا 11 رقمی باشد',
  })
  landline_phone?: string;

  @ApiProperty({
    example: '1234567890',
    description: 'کد پستی (اجباری)',
  })
  @IsNotEmpty({ message: 'کد پستی اجباری است' })
  @IsString({ message: 'کد پستی باید رشته باشد' })
  @Matches(/^\d{10}$/, {
    message: 'کود پستی معتبر نیست. باید 10 رقمی باشد',
  })
  postal_code: string;
}

export class UpdateAffiliateDto {
  @ApiProperty({
    example: 'John',
    description: 'نام (اجباری برای اشخاص حقیقی)',
  })
  @IsOptional()
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  name?: string;

  @ApiProperty({
    example: 'Doe',
    description: 'نام خانوادگی (اجباری برای اشخاص حقیقی)',
  })
  @IsOptional()
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  family?: string;

  @ApiProperty({
    example: '<EMAIL>',
    description: 'ایمیل (اختیاری)',
  })
  @IsOptional()
  @IsEmail({}, { message: 'ایمیل معتبر نیست' })
  email?: string;

  @ApiProperty({
    example: EntityTypeEnum.PERSON,
    description: 'نوع نهاد',
    enum: EntityTypeEnum,
  })
  @IsOptional()
  @IsEnum(EntityTypeEnum, {
    message: 'نوع نهاد باید یکی از مقادیر PERSON یا CORPORATION باشد',
  })
  entity_type?: EntityTypeEnum;

  @ApiProperty({
    example: '123456789',
    description: 'کد ثبت شرکت (اجباری برای اشخاص حقوقی)',
  })
  @IsOptional()
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  registration_code?: string;

  @ApiProperty({
    example: 'شرکت نمونه',
    description: 'نام شرکت (اجباری برای اشخاص حقوقی)',
  })
  @IsOptional()
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  corporation_name?: string;

  @ApiProperty({
    example: 8,
    description: 'شناسه استان',
  })
  @IsOptional()
  @IsNumber({}, { message: 'شناسه استان باید عدد باشد' })
  province_id?: number;

  @ApiProperty({
    example: 45,
    description: 'شناسه شهر',
  })
  @IsOptional()
  @IsNumber({}, { message: 'شناسه شهر باید عدد باشد' })
  city_id?: number;

  @ApiProperty({
    example: 'خیابان ولیعصر، پلاک 123',
    description: 'آدرس',
  })
  @IsOptional()
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  address?: string;

  @ApiProperty({
    example: 'توضیحات اضافی',
    description: 'توضیحات (اختیاری)',
  })
  @IsOptional()
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  description?: string;

  @ApiProperty({
    example: UserStatusEnum.PENDING,
    description: 'وضعیت (فقط برای ادمین)',
    enum: UserStatusEnum,
  })
  @IsOptional()
  @IsEnum(UserStatusEnum, {
    message: 'وضعیت باید یکی از مقادیر معتبر باشد',
  })
  status?: UserStatusEnum;

  @ApiProperty({
    example: '1234567890',
    description: 'کد ملی (اجباری برای اشخاص حقیقی)',
  })
  @IsOptional()
  @IsString({ message: 'کد ملی باید رشته باشد' })
  @Matches(/^\d{10}$/, {
    message: 'کد ملی معتبر نیست. باید 10 رقمی باشد',
  })
  national_code?: string;

  @ApiProperty({
    example: '02112345678',
    description: 'شماره تلفن ثابت (اختیاری)',
  })
  @IsOptional()
  @IsString({ message: 'شماره تلفن ثابت باید رشته باشد' })
  @Matches(/^\d{8,11}$/, {
    message: 'شماره تلفن ثابت معتبر نیست. باید 8 تا 11 رقمی باشد',
  })
  landline_phone?: string;

  @ApiProperty({
    example: '1234567890',
    description: 'کد پستی (اختیاری)',
  })
  @IsOptional()
  @IsString({ message: 'کد پستی باید رشته باشد' })
  @Matches(/^\d{10}$/, {
    message: 'کد پستی معتبر نیست. باید 10 رقمی باشد',
  })
  postal_code?: string;
}

export class AffiliateResponseDto implements MappedWithEntity<affiliate_info> {
  @ApiProperty({ example: 1, description: 'شناسه اطلاعات همکار' })
  @Mapped()
  id: number;

  @ApiProperty({
    example: EntityTypeEnum.PERSON,
    description: 'نوع نهاد',
    enum: EntityTypeEnum,
  })
  @Mapped()
  entity_type: EntityTypeEnum;

  @ApiProperty({ example: '123456789', description: 'کد ثبت شرکت' })
  @Mapped()
  registration_code?: string;

  @ApiProperty({ example: 'شرکت نمونه', description: 'نام شرکت' })
  @Mapped()
  corporation_name?: string;

  @ApiProperty({ example: 'خیابان ولیعصر، پلاک 123', description: 'آدرس' })
  @Mapped()
  address: string;

  @ApiProperty({ example: 'توضیحات اضافی', description: 'توضیحات' })
  @Mapped()
  description?: string;

  @ApiProperty({ example: '1234567890', description: 'کد ملی' })
  @Mapped()
  national_code?: string;

  @ApiProperty({ example: '02112345678', description: 'شماره تلفن ثابت' })
  @Mapped()
  landline_phone?: string;

  @ApiProperty({ example: '1234567890', description: 'کد پستی' })
  @Mapped()
  postal_code: string;

  // Province and city information
  @ApiProperty({
    example: { id: 8, title: 'تهران' },
    description: 'اطلاعات استان',
    required: false,
  })
  province?: {
    id: number;
    title: string;
  };

  @ApiProperty({
    example: { id: 45, title: 'تبریز' },
    description: 'اطلاعات شهر',
    required: false,
  })
  city?: {
    id: number;
    title: string;
  };

  // User information
  @ApiProperty({
    description: 'اطلاعات کاربر مرتبط با همکار',
    required: false,
    type: () => ({
      id: { type: Number, description: 'شناسه کاربر' },
      name: { type: String, description: 'نام', required: false },
      family: { type: String, description: 'نام خانوادگی', required: false },
      phone: { type: String, description: 'شماره تلفن' },
      email: { type: String, description: 'ایمیل', required: false },
      status: {
        type: 'enum',
        enum: UserStatusEnum,
        description: 'وضعیت کاربر',
      },
    }),
  })
  @Mapped()
  user?: {
    id: number;
    name?: string;
    family?: string;
    phone: string;
    email?: string;
    status: UserStatusEnum;
  };
}

export class AffiliateListResponseDto {
  @ApiProperty({
    type: [AffiliateResponseDto],
    description: 'لیست همکاران',
  })
  data: AffiliateResponseDto[];

  @ApiProperty({ example: 100, description: 'تعداد کل همکاران' })
  total: number;

  @ApiProperty({ example: 1, description: 'صفحه فعلی' })
  page: number;

  @ApiProperty({ example: 10, description: 'تعداد آیتم در هر صفحه' })
  limit: number;

  @ApiProperty({ example: 10, description: 'تعداد کل صفحات' })
  totalPages: number;
}

export class SearchAffiliatesDto {
  @ApiProperty({
    example: '9123456789',
    description: 'عبارت جستجو (شماره تلفن یا ایمیل)',
  })
  @IsNotEmpty({ message: 'عبارت جستجو اجباری است' })
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  q: string;
}

export class AffiliateFiltersDto {
  @ApiProperty({
    example: EntityTypeEnum.PERSON,
    description: 'نوع نهاد',
    enum: EntityTypeEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(EntityTypeEnum, {
    message: 'نوع نهاد باید یکی از مقادیر PERSON یا CORPORATION باشد',
  })
  entity_type?: EntityTypeEnum;

  @ApiProperty({
    example: UserStatusEnum.PENDING,
    description: 'وضعیت',
    enum: UserStatusEnum,
    required: false,
  })
  @IsOptional()
  @IsEnum(UserStatusEnum, {
    message: 'وضعیت باید یکی از مقادیر معتبر باشد',
  })
  status?: UserStatusEnum;

  @ApiProperty({
    example: 8,
    description: 'شناسه استان',
  })
  @IsOptional()
  @IsNumber({}, { message: 'شناسه استان باید عدد باشد' })
  province_id?: number;

  @ApiProperty({
    example: 45,
    description: 'شناسه شهر',
  })
  @IsOptional()
  @IsNumber({}, { message: 'شناسه شهر باید عدد باشد' })
  city_id?: number;

  @ApiProperty({
    example: 'john',
    description: 'عبارت جستجو',
  })
  @IsOptional()
  @IsString({ message: 'عبارت جستجو باید رشته باشد' })
  search?: string;
}

export function mapToAffiliateResponseDto(
  affiliateInfo: affiliate_info,
  user: users,
): AffiliateResponseDto {
  return {
    id: affiliateInfo.id,
    entity_type: affiliateInfo.entity_type,
    registration_code: affiliateInfo.registration_code,
    corporation_name: affiliateInfo.corporation_name,
    address: affiliateInfo.address,
    description: affiliateInfo.description,
    postal_code: affiliateInfo.postal_code,
    landline_phone: affiliateInfo.landline_phone,
    national_code: affiliateInfo.national_code,
    province: affiliateInfo.province
      ? {
          id: affiliateInfo.province.id,
          title: affiliateInfo.province.title,
        }
      : undefined,
    city: affiliateInfo.city
      ? {
          id: affiliateInfo.city.id,
          title: affiliateInfo.city.title,
        }
      : undefined,
    user: {
      id: affiliateInfo.id,
      name: user.name,
      family: user.family,
      phone: user.phone,
      email: user.email,
      status: user.status,
    },
  };
}
