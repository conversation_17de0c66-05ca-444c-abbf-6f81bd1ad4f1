import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { AffiliateController } from './affiliate.controller';
import { AffiliateService } from './affiliate.service';
import { affiliate_info } from '../base/database/entities/affiliate-info.entity';
import { users } from '../base/database/entities/users.entity';
import { province_cities } from '../base/database/entities/province-cities.entity';
import { discounts } from '../base/database/entities/discounts.entity';
import { payments } from '../base/database/entities/payments.entity';
import { TokenModule } from '../token/token.module';
import { TokenService } from 'src/token/token.service';

@Module({
  imports: [
    TypeOrmModule.forFeature([
      affiliate_info,
      users,
      province_cities,
      discounts,
      payments,
    ]),
    TokenModule,
  ],
  controllers: [AffiliateController],
  providers: [AffiliateService, TokenService],
  exports: [AffiliateService],
})
export class AffiliateModule {}
