import {
  Injectable,
  BadRequestException,
  NotFoundException,
  ConflictException,
  ForbiddenException,
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { affiliate_info } from '../base/database/entities/affiliate-info.entity';
import { users } from '../base/database/entities/users.entity';
import { province_cities } from '../base/database/entities/province-cities.entity';
import { discounts } from '../base/database/entities/discounts.entity';
import { payments } from '../base/database/entities/payments.entity';
import {
  EntityTypeEnum,
  RoleEnum,
  UserStatusEnum,
} from '../base/enums/user.enums';
import { PaymentStateEnum } from '../base/enums/purchase-state.enum';
import { DiscountStateEnum } from '../base/database/entities/discounts.entity';
import {
  CreateAffiliateDto,
  UpdateAffiliateDto,
  AffiliateResponseDto,
  AffiliateListResponseDto,
  AffiliateFiltersDto,
  mapToAffiliateResponseDto,
} from './affiliate.dto';
import { AffiliateAnalyticsQueryDto } from './dto/affiliate-analytics.query.dto';
import {
  AffiliateDashboardResponseDto,
  SalesItemDto,
} from './dto/affiliate-dashboard.response.dto';
import {
  AffiliateDiscountCodesResponseDto,
  AffiliateDiscountCodesSearchDto,
} from './dto/affiliate-discount-codes.response.dto';
import {
  AffiliateCommissionsResponseDto,
  CommissionItemDto,
} from './dto/affiliate-commissions.response.dto';
import { PaginationMetaDto } from './dto/paginiation.dto';
import { groupNumberByThreeDigits } from 'src/base/utils/convert-info';
import { DiscountCodeItemDto } from './dto/affiliate-discount-codes.response.dto';

@Injectable()
export class AffiliateService {
  constructor(
    @InjectRepository(affiliate_info)
    private affiliateInfoRepository: Repository<affiliate_info>,
    @InjectRepository(users)
    private usersRepository: Repository<users>,
    @InjectRepository(province_cities)
    private provinceCitiesRepository: Repository<province_cities>,
    @InjectRepository(discounts)
    private discountsRepository: Repository<discounts>,
    @InjectRepository(payments)
    private paymentsRepository: Repository<payments>,
    private dataSource: DataSource,
  ) {}

  async registerAffiliate(
    createAffiliateDto: CreateAffiliateDto,
    isAdmin: boolean = false,
  ): Promise<AffiliateResponseDto> {
    // Validate entity type specific fields
    this.validateEntityTypeFields(createAffiliateDto);

    // Validate province and city
    await this.validateProvinceAndCity(
      createAffiliateDto.province_id,
      createAffiliateDto.city_id,
    );

    // Check if user with this phone already exists
    const existingUser = await this.usersRepository.findOne({
      where: { phone: createAffiliateDto.phone },
    });

    let user: users;

    if (existingUser) {
      // Check if user already has AFFILIATE role
      if (existingUser.roles.includes(RoleEnum.AFFILIATE)) {
        throw new ConflictException(
          'این کاربر قبلاً به عنوان همکار ثبت شده است',
        );
      }

      // User exists but doesn't have AFFILIATE role - add it and update user data
      // Check if email already exists for another user (if provided)
      if (
        createAffiliateDto.email &&
        createAffiliateDto.email !== existingUser.email
      ) {
        const existingEmailUser = await this.usersRepository.findOne({
          where: { email: createAffiliateDto.email },
        });

        if (existingEmailUser && existingEmailUser.id !== existingUser.id) {
          throw new ConflictException('کاربری با این ایمیل قبلاً ثبت شده است');
        }
      }

      // Determine status based on admin privileges
      const status =
        isAdmin && createAffiliateDto.status
          ? createAffiliateDto.status
          : UserStatusEnum.PENDING;

      // Add AFFILIATE role to existing roles, removing USER if present
      existingUser.roles = [
        ...existingUser.roles.filter((r) => r !== RoleEnum.USER),
        RoleEnum.AFFILIATE,
      ];
      existingUser.name = createAffiliateDto.name || existingUser.name;
      existingUser.family = createAffiliateDto.family || existingUser.family;
      existingUser.email = createAffiliateDto.email || existingUser.email;
      existingUser.status = status;

      user = await this.usersRepository.save(existingUser);
    } else {
      // No existing user - check email uniqueness (if provided)
      if (createAffiliateDto.email) {
        const existingEmailUser = await this.usersRepository.findOne({
          where: { email: createAffiliateDto.email },
        });

        if (existingEmailUser) {
          throw new ConflictException('کاربری با این ایمیل قبلاً ثبت شده است');
        }
      }

      // Determine status based on admin privileges
      const status =
        isAdmin && createAffiliateDto.status
          ? createAffiliateDto.status
          : UserStatusEnum.PENDING;

      // Create new user
      const newUser = this.usersRepository.create({
        name: createAffiliateDto.name,
        family: createAffiliateDto.family,
        phone: createAffiliateDto.phone,
        email: createAffiliateDto.email,
        country_code: '+98', // Default to Iran
        roles: [RoleEnum.AFFILIATE],
        status: status,
      });

      user = await this.usersRepository.save(newUser);
    }

    // Create affiliate info
    const affiliateInfo = this.affiliateInfoRepository.create({
      entity_type: createAffiliateDto.entity_type,
      registration_code: createAffiliateDto.registration_code,
      corporation_name: createAffiliateDto.corporation_name,
      province_id: createAffiliateDto.province_id,
      city_id: createAffiliateDto.city_id,
      address: createAffiliateDto.address,
      description: createAffiliateDto.description,
      user_id: user.id,
    });

    const savedAffiliateInfo =
      await this.affiliateInfoRepository.save(affiliateInfo);

    return mapToAffiliateResponseDto(savedAffiliateInfo, user);
  }

  async getAffiliate(id: number): Promise<AffiliateResponseDto> {
    const affiliateInfo = await this.affiliateInfoRepository.findOne({
      where: { id },
      relations: ['user', 'province', 'city'],
    });

    if (!affiliateInfo) {
      throw new NotFoundException('همکار مورد نظر یافت نشد');
    }

    return mapToAffiliateResponseDto(affiliateInfo, affiliateInfo.user);
  }

  async updateAffiliate(
    id: number,
    updateAffiliateDto: UpdateAffiliateDto,
    isAdmin: boolean = false,
  ): Promise<AffiliateResponseDto> {
    const affiliateInfo = await this.affiliateInfoRepository.findOne({
      where: { id },
      relations: ['user', 'province', 'city'],
    });

    if (!affiliateInfo) {
      throw new NotFoundException('همکار مورد نظر یافت نشد');
    }

    // Validate province and city if they are being updated
    if (updateAffiliateDto.province_id || updateAffiliateDto.city_id) {
      const provinceId =
        updateAffiliateDto.province_id ?? affiliateInfo.province_id;
      const cityId = updateAffiliateDto.city_id ?? affiliateInfo.city_id;
      await this.validateProvinceAndCity(provinceId, cityId);
    }

    // Validate entity type specific fields if entity_type is being updated
    if (updateAffiliateDto.entity_type) {
      const validationDto = {
        ...updateAffiliateDto,
        entity_type: updateAffiliateDto.entity_type,
      } as CreateAffiliateDto;
      this.validateEntityTypeFields(validationDto);
    }

    // Update user information
    if (updateAffiliateDto.name !== undefined) {
      affiliateInfo.user.name = updateAffiliateDto.name;
    }
    if (updateAffiliateDto.family !== undefined) {
      affiliateInfo.user.family = updateAffiliateDto.family;
    }
    if (updateAffiliateDto.email !== undefined) {
      // Check if email already exists for another user
      if (updateAffiliateDto.email) {
        const existingEmailUser = await this.usersRepository.findOne({
          where: { email: updateAffiliateDto.email },
        });

        if (
          existingEmailUser &&
          existingEmailUser.id !== affiliateInfo.user.id
        ) {
          throw new ConflictException('کاربری با این ایمیل قبلاً ثبت شده است');
        }
      }
      affiliateInfo.user.email = updateAffiliateDto.email;
    }

    // Update status if provided (admin only, but enforced via guards)
    if (updateAffiliateDto.status !== undefined && isAdmin) {
      affiliateInfo.user.status = updateAffiliateDto.status;
    }

    // Update affiliate info
    Object.assign(affiliateInfo, {
      entity_type: updateAffiliateDto.entity_type ?? affiliateInfo.entity_type,
      registration_code:
        updateAffiliateDto.registration_code ?? affiliateInfo.registration_code,
      corporation_name:
        updateAffiliateDto.corporation_name ?? affiliateInfo.corporation_name,
      province_id: updateAffiliateDto.province_id ?? affiliateInfo.province_id,
      city_id: updateAffiliateDto.city_id ?? affiliateInfo.city_id,
      address: updateAffiliateDto.address ?? affiliateInfo.address,
      description: updateAffiliateDto.description ?? affiliateInfo.description,
    });

    await this.usersRepository.save(affiliateInfo.user);
    const updatedAffiliateInfo =
      await this.affiliateInfoRepository.save(affiliateInfo);

    return mapToAffiliateResponseDto(updatedAffiliateInfo, affiliateInfo.user);
  }

  async listAffiliates(
    page: number = 1,
    limit: number = 10,
    filters?: AffiliateFiltersDto,
  ): Promise<AffiliateListResponseDto> {
    const queryBuilder = this.affiliateInfoRepository
      .createQueryBuilder('affiliate_info')
      .leftJoinAndSelect('affiliate_info.user', 'user')
      .leftJoinAndSelect('affiliate_info.province', 'province')
      .leftJoinAndSelect('affiliate_info.city', 'city');

    // Apply filters
    if (filters?.entity_type) {
      queryBuilder.andWhere('affiliate_info.entity_type = :entity_type', {
        entity_type: filters.entity_type,
      });
    }
    if (filters?.status) {
      queryBuilder.andWhere('user.status = :status', {
        status: filters.status,
      });
    }
    if (filters?.province_id) {
      queryBuilder.andWhere('affiliate_info.province_id = :province_id', {
        province_id: filters.province_id,
      });
    }
    if (filters?.city_id) {
      queryBuilder.andWhere('affiliate_info.city_id = :city_id', {
        city_id: filters.city_id,
      });
    }
    if (filters?.search) {
      queryBuilder.andWhere(
        '(user.name ILIKE :search OR user.family ILIKE :search OR user.phone ILIKE :search OR user.email ILIKE :search)',
        { search: `%${filters.search}%` },
      );
    }

    const [affiliateInfos, total] = await queryBuilder
      .skip((page - 1) * limit)
      .take(limit)
      .orderBy('affiliate_info.created_at', 'DESC')
      .getManyAndCount();

    const data = affiliateInfos.map((affiliateInfo) =>
      mapToAffiliateResponseDto(affiliateInfo, affiliateInfo.user),
    );

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getDiscountCodes(
    user: { id: number },
    query: AffiliateDiscountCodesSearchDto,
  ): Promise<AffiliateDiscountCodesResponseDto> {
    const affiliateId = await this.resolveAffiliateId(user.id);
    const { from, to } = this.parseDateFilters(query);
    const page = query.page || 1;
    const limit = Math.min(query.limit || 20, 100);
    const offset = (page - 1) * limit;

    // Get total count
    const totalItemsQuery = this.discountsRepository
      .createQueryBuilder('d')
      .where('d.referral_id = :affiliateId', { affiliateId });

    if (from) {
      totalItemsQuery.andWhere('d.created_at >= :from', { from });
    }
    if (to) {
      totalItemsQuery.andWhere('d.created_at <= :to', { to });
    }

    const totalItems = await totalItemsQuery.getCount();

    // Get aggregates
    const totalDiscountCodes = totalItems;

    const soldDiscountCodesQuery = this.discountsRepository
      .createQueryBuilder('d')
      .where('d.referral_id = :affiliateId', { affiliateId })
      .andWhere('d.state = :state', { state: DiscountStateEnum.USED });

    if (from) {
      soldDiscountCodesQuery.andWhere('d.created_at >= :from', { from });
    }
    if (to) {
      soldDiscountCodesQuery.andWhere('d.created_at <= :to', { to });
    }

    const soldDiscountCodes = await soldDiscountCodesQuery.getCount();

    // Get commission earned
    const commissionQuery = this.paymentsRepository
      .createQueryBuilder('p')
      .leftJoin('p.discount', 'd')
      .select(
        'COALESCE(SUM(CAST(p.price AS numeric) * CAST(d.commission_percent AS numeric) / 100 / 10), 0)',
        'total',
      )
      .where('d.referral_id = :affiliateId', { affiliateId })
      .andWhere('p.state = :state', { state: PaymentStateEnum.SUCCESS })
      .andWhere('p.discount_id IS NOT NULL');

    if (from) {
      commissionQuery.andWhere('p.created_at >= :from', { from });
    }
    if (to) {
      commissionQuery.andWhere('p.created_at <= :to', { to });
    }

    const commissionResult = await commissionQuery.getRawOne();
    const totalCommissionEarned = groupNumberByThreeDigits(
      Number(commissionResult?.total?.toString() || '0').toFixed(2),
    );

    // Get paginated data
    const dataQuery = this.discountsRepository
      .createQueryBuilder('d')
      .select([
        'd.id as discount_id',
        'd.code as code',
        'd.state as state',
        'd.commission_percent as commission_percent',
        'd.expire_date as expire_date',
        'd.created_at as created_at',
      ])
      .where('d.referral_id = :affiliateId', { affiliateId })
      .orderBy('d.created_at', 'DESC')
      .offset(offset)
      .limit(limit);

    if (query.state) {
      dataQuery.andWhere('d.state = :state', { state: query.state });
    }

    if (from) {
      dataQuery.andWhere('d.created_at >= :from', { from });
    }
    if (to) {
      dataQuery.andWhere('d.created_at <= :to', { to });
    }

    const rawData = await dataQuery.getRawMany();
    const data = rawData.map<DiscountCodeItemDto>((item) => ({
      // ...item,
      discountId: item.discount_id,
      code: item.code,
      state: item.state,
      commissionPercent: item.commission_percent,
      expireDate: item.expire_date,
      createdAt: item.created_at,
    }));

    return {
      totalDiscountCodes,
      soldDiscountCodes,
      totalCommissionEarned,
      data,
      pagination: this.buildPaginationMeta(page, limit, totalItems),
    };
  }

  async getCommissions(
    user: { id: number },
    query: AffiliateAnalyticsQueryDto,
  ): Promise<AffiliateCommissionsResponseDto> {
    const affiliateId = await this.resolveAffiliateId(user.id);
    const { from, to } = this.parseDateFilters(query);
    const page = query.page || 1;
    const limit = Math.min(query.limit || 20, 100);
    const offset = (page - 1) * limit;

    // Get total count
    const totalItemsQuery = this.paymentsRepository
      .createQueryBuilder('p')
      .leftJoin('p.discount', 'd')
      .where('d.referral_id = :affiliateId', { affiliateId })
      .andWhere('p.discount_id IS NOT NULL')
      .andWhere('p.state = :state', { state: PaymentStateEnum.SUCCESS });

    if (from) {
      totalItemsQuery.andWhere('p.created_at >= :from', { from });
    }
    if (to) {
      totalItemsQuery.andWhere('p.created_at <= :to', { to });
    }

    const totalItems = await totalItemsQuery.getCount();

    // Get aggregates
    const totalDiscountCodesQuery = this.discountsRepository
      .createQueryBuilder('d')
      .where('d.referral_id = :affiliateId', { affiliateId });

    if (from) {
      totalDiscountCodesQuery.andWhere('d.created_at >= :from', { from });
    }
    if (to) {
      totalDiscountCodesQuery.andWhere('d.created_at <= :to', { to });
    }

    const totalDiscountCodes = await totalDiscountCodesQuery.getCount();

    const soldDiscountCodesQuery = this.discountsRepository
      .createQueryBuilder('d')
      .where('d.referral_id = :affiliateId', { affiliateId })
      .andWhere('d.state = :state', { state: DiscountStateEnum.USED });

    if (from) {
      soldDiscountCodesQuery.andWhere('d.created_at >= :from', { from });
    }
    if (to) {
      soldDiscountCodesQuery.andWhere('d.created_at <= :to', { to });
    }

    const soldDiscountCodes = await soldDiscountCodesQuery.getCount();

    // Get commission earned
    const commissionQuery = this.paymentsRepository
      .createQueryBuilder('p')
      .leftJoin('p.discount', 'd')
      .select(
        'COALESCE(SUM(CAST(p.price AS numeric) * CAST(d.commission_percent AS numeric) / 100 / 10), 0)',
        'total',
      )
      .where('d.referral_id = :affiliateId', { affiliateId })
      .andWhere('p.state = :state', { state: PaymentStateEnum.SUCCESS })
      .andWhere('p.discount_id IS NOT NULL');

    if (from) {
      commissionQuery.andWhere('p.created_at >= :from', { from });
    }
    if (to) {
      commissionQuery.andWhere('p.created_at <= :to', { to });
    }

    const commissionResult = await commissionQuery.getRawOne();
    const totalCommissionEarned = groupNumberByThreeDigits(
      Number(commissionResult?.total?.toString() || '0').toFixed(2),
    );

    // Get paginated data
    const dataQuery = this.paymentsRepository
      .createQueryBuilder('p')
      .leftJoin('p.discount', 'd')
      .select([
        'p.id as payment_id',
        'd.id as discount_id',
        'd.code as code',
        'p.price as price',
        'd.commission_percent as commission_percent',
        'CAST(p.price AS numeric) * CAST(d.commission_percent AS numeric) / 100 / 10 as commission_amount',
        'p.created_at as created_at',
      ])
      .where('d.referral_id = :affiliateId', { affiliateId })
      .andWhere('p.discount_id IS NOT NULL')
      .andWhere('p.state = :state', { state: PaymentStateEnum.SUCCESS })
      .orderBy('p.created_at', 'DESC')
      .offset(offset)
      .limit(limit);

    if (from) {
      dataQuery.andWhere('p.created_at >= :from', { from });
    }
    if (to) {
      dataQuery.andWhere('p.created_at <= :to', { to });
    }

    const rawData = await dataQuery.getRawMany();
    const data = rawData.map<CommissionItemDto>((item) => ({
      paymentId: item.payment_id,
      discountId: item.discount_id,
      code: item.code,
      price: item.price / 10,
      commissionPercent: item.commission_percent,
      commissionAmount: groupNumberByThreeDigits(
        Number(item.commission_amount?.toString() || '0').toFixed(2),
      ),
      createdAt: item.created_at,
    }));

    return {
      totalDiscountCodes,
      soldDiscountCodes,
      totalCommissionEarned,
      data,
      pagination: this.buildPaginationMeta(page, limit, totalItems),
    };
  }

  async getDashboard(user: {
    id: number;
  }): Promise<AffiliateDashboardResponseDto> {
    const affiliateId = await this.resolveAffiliateId(user.id);

    // Get aggregates
    const totalDiscountCodesQuery = this.discountsRepository
      .createQueryBuilder('d')
      .where('d.referral_id = :affiliateId', { affiliateId });

    const totalDiscountCodes = await totalDiscountCodesQuery.getCount();

    const soldDiscountCodesQuery = this.discountsRepository
      .createQueryBuilder('d')
      .where('d.referral_id = :affiliateId', { affiliateId })
      .andWhere('d.state = :state', { state: DiscountStateEnum.USED });

    const soldDiscountCodes = await soldDiscountCodesQuery.getCount();

    // Get commission earned
    const commissionQuery = this.paymentsRepository
      .createQueryBuilder('p')
      .leftJoin('p.discount', 'd')
      .select(
        'COALESCE(SUM(CAST(p.price AS numeric) * CAST(d.commission_percent AS numeric) / 100 / 10), 0)',
        'total',
      )
      .where('d.referral_id = :affiliateId', { affiliateId })
      .andWhere('p.state = :state', { state: PaymentStateEnum.SUCCESS })
      .andWhere('p.discount_id IS NOT NULL');
    const commissionResult = await commissionQuery.getRawOne();
    const totalCommissionEarned = groupNumberByThreeDigits(
      Number(commissionResult?.total?.toString() || '0').toFixed(2),
    );

    const dataQuery = this.paymentsRepository
      .createQueryBuilder('p')
      .leftJoin('p.discount', 'd')
      .select([
        'p.id as payment_id',
        'd.id as discount_id',
        'd.code as code',
        'p.price as price',
        'd.commission_percent as commission_percent',
        'CAST(p.price AS numeric) * CAST(d.commission_percent AS numeric) / 100 / 10 as commission_amount',
        'p.created_at as created_at',
      ])
      .where('d.referral_id = :affiliateId', { affiliateId })
      .andWhere('p.discount_id IS NOT NULL')
      .andWhere('p.state = :state', { state: PaymentStateEnum.SUCCESS })
      .limit(10);

    const rawData = await dataQuery.getRawMany();

    const data = rawData.map<SalesItemDto>((item) => ({
      paymentId: item.payment_id,
      discountId: item.discount_id,
      code: item.code,
      price: item.price / 10,
      finalPrice: item.final_price / 10,
      paymentState: item.payment_state,
      commissionPercent: item.commission_percent,
      commissionAmount: groupNumberByThreeDigits(
        Number(item.commission_amount?.toString() || '0').toFixed(2),
      ),
      createdAt: item.created_at,
    }));

    return {
      totalDiscountCodes,
      soldDiscountCodes,
      totalCommissionEarned,
      sales: data,
    };
  }

  private async resolveAffiliateId(userId: number): Promise<number> {
    const affiliateInfo = await this.affiliateInfoRepository.findOne({
      where: { user_id: userId },
    });

    if (!affiliateInfo) {
      throw new ForbiddenException('Affiliate profile not found');
    }

    return affiliateInfo.id;
  }

  private buildPaginationMeta(
    page: number,
    limit: number,
    totalItems: number,
  ): PaginationMetaDto {
    const totalPages = Math.ceil(totalItems / limit);
    return {
      page,
      limit,
      totalItems,
      totalPages,
    };
  }

  private parseDateFilters(query: AffiliateAnalyticsQueryDto): {
    from?: Date;
    to?: Date;
  } {
    const filters: { from?: Date; to?: Date } = {};
    if (query.from) {
      filters.from = new Date(query.from);
    }
    if (query.to) {
      filters.to = new Date(query.to);
    }
    return filters;
  }

  private async validateProvinceAndCity(
    provinceId: number,
    cityId: number,
  ): Promise<void> {
    // Check if province exists and has parent = 0
    const province = await this.provinceCitiesRepository.findOne({
      where: { id: provinceId, parent: 0 },
    });

    if (!province) {
      throw new BadRequestException('استان مورد نظر یافت نشد');
    }

    // Check if city exists and belongs to the specified province
    const city = await this.provinceCitiesRepository.findOne({
      where: { id: cityId, parent: provinceId },
    });

    if (!city) {
      throw new BadRequestException('شهر مورد نظر در استان انتخابی یافت نشد');
    }
  }

  private validateEntityTypeFields(dto: CreateAffiliateDto): void {
    if (dto.entity_type === EntityTypeEnum.PERSON) {
      if (!dto.name || !dto.family) {
        throw new BadRequestException(
          'نام و نام خانوادگی برای اشخاص حقیقی اجباری است',
        );
      }
      if (!dto.national_code || dto.national_code.trim() === '') {
        throw new BadRequestException('کد ملی برای اشخاص حقیقی اجباری است');
      }
    } else if (dto.entity_type === EntityTypeEnum.CORPORATION) {
      if (!dto.registration_code || !dto.corporation_name) {
        throw new BadRequestException(
          'کد ثبت شرکت و نام شرکت برای اشخاص حقوقی اجباری است',
        );
      }
    }

    // Validate postal_code for both entity types
    if (!dto.postal_code || dto.postal_code.trim() === '') {
      throw new BadRequestException('کد پستی اجباری است');
    }
  }
}
