import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { AuthModule } from './auth/auth.module';
import { TypeOrmModule } from '@nestjs/typeorm'; //
import ormconfig from './base/ormconfig';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { UsersModule } from './user/user.module';
import { EmailModule } from './base/webServices/email/email.module';
import { TokenModule } from './token/token.module';
import { CustomLogger } from './base/logger/custom-logger';
import { LogModule } from './base/logger/log.module';
import { PurchaseModule } from './purchase/purchase.module';
import { SeedsModule } from './seeds/seeds.module';
import { ProductModule } from './product/product.module';
import { DiscountModule } from './discount/discount.module';
import { AdminModule } from './admin/admin.module';
import { PaymentModule } from './payment/payment.module';
import { AffiliateModule } from './affiliate/affiliate.module';
import { RedisModule } from '@liaoliaots/nestjs-redis';
import { ProvinceModule } from './province/province.module';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath:
        process.env.NODE_ENV === 'development' ? '.env.development' : '.env',
      load: [ormconfig],
    }),
    TypeOrmModule.forRootAsync({
      inject: [ConfigService],
      useFactory: async (configService: ConfigService) =>
        configService.get('typeorm'),
    }),
    JwtModule.register({
      global: true,
      secret: process.env.SECRET_KEY,
      signOptions: { expiresIn: '15h' },
    }),
    AuthModule,
    UsersModule,
    EmailModule,
    TokenModule,
    LogModule,
    PurchaseModule,
    ProductModule,
    SeedsModule,
    DiscountModule,
    AdminModule,
    PaymentModule,
    AffiliateModule,
    ProvinceModule,
    RedisModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        config: {
          host: configService.get('REDIS_HOST', 'localhost'),
          port: configService.get('REDIS_PORT', 6379),
          url: configService.get('REDIS_URL'),
        },
      }),
      inject: [ConfigService],
    }),
  ],
  controllers: [AppController],
  providers: [AppService, CustomLogger],
})
export class AppModule {}
