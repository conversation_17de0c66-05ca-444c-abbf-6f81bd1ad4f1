import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { PurchaseService } from './purchase.service';
import { PurchaseController } from './purchase.controller';
import { TokenService } from '../token/token.service';
import { BazarModule } from '../base/webServices/bazar/bazar.module';
import { LogModule } from '../base/logger/log.module';
import { PurchasesRepository } from './purchase.repository';
import { TypeOrmModule } from '@nestjs/typeorm';
import { purchases } from '../base/database/entities/purchase.entity';
import { MapperService } from '../base/utils/mapper/mapper.service';
import { ProductModule } from '../product/product.module';
import { DiscountRepository } from 'src/discount/discount.repository';
import { PaymentsRepository } from 'src/payment/payment.repository';
import { DiscountService } from 'src/discount/discount.service';
import { discounts } from 'src/base/database/entities/discounts.entity';
import { payments } from 'src/base/database/entities/payments.entity';
@Module({
  imports: [
    HttpModule,
    BazarModule,
    LogModule,
    TypeOrmModule.forFeature([purchases, discounts, payments]),
    ProductModule,
  ],
  providers: [
    PurchaseService,
    PurchasesRepository,
    TokenService,
    MapperService,
    DiscountService,
    DiscountRepository,
    PaymentsRepository,
  ],
  controllers: [PurchaseController],
  exports: [PurchaseService],
})
export class PurchaseModule {}
