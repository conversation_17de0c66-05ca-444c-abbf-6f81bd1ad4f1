import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsOptional } from 'class-validator';
import { Timestamp } from 'typeorm';
import {
  PaymentFromEnum,
  PurchaseStateEnum,
} from '../base/enums/purchase-state.enum';
import { Mapped } from '../base/utils/mapper/mapper.decorator';
import { ProductOutputDto } from '../product/product.dto';
import { Type } from 'class-transformer';
import { PaymentOutputDto } from '../payment/payment.dto';

export class PurchaseInputDto {
  @IsOptional()
  @ApiProperty({ example: 'package_name' })
  package_name: string;

  @ApiProperty()
  product_id: number;

  @ApiProperty({ example: 'purchase_token' })
  purchase_token: string;

  @IsOptional()
  @ApiProperty({ example: 'order_id' })
  order_id: string;

  @IsOptional()
  @ApiProperty({ example: 'price' })
  price?: string;

  @IsEnum(PaymentFromEnum, {
    message: 'نوع پرداخت باید یکی از مقادیر SIBAPP یا BAZAR یا ZARINPAL باشد',
  })
  @IsOptional()
  @ApiProperty({ default: PaymentFromEnum.BAZAR })
  payment_from?: PaymentFromEnum;
}

export class SibAppPurchaseInputDto {
  @IsNotEmpty()
  @ApiProperty({ example: 'package_name' })
  package_name: string;

  @ApiProperty()
  product_id: number;

  @ApiProperty({ example: 'purchase_id' })
  purchase_id: string;

  @IsNotEmpty()
  @ApiProperty({ example: 'phone_number' })
  phone_number: string;
}

export class PurchaseOutputDto {
  consumptionState: string;
  purchaseState: number;
  developerPayload: string;
  purchaseTime: Timestamp;
}
export class PurchaseSaveInputDto {
  packageName?: string;
  product_id: number;
  payment_id?: number;
  user_id: number;
  token?: string;
  purchaseTime?: Timestamp;
  consumptionState?: string;
  state: PurchaseStateEnum;
  payload?: string;
  time: Date;
  orderId?: string;
}

export class PurchasesOutputDto {
  @ApiProperty()
  @Mapped()
  orderId: string;
  @ApiProperty({
    example: 'PURCHASED',
    description: 'وضعیت خرید کاربر',
    enum: PurchaseStateEnum,
  })
  @Mapped()
  state: PurchaseStateEnum;

  @ApiProperty()
  @Mapped()
  token: string;

  @ApiProperty()
  @Mapped()
  time: Date;

  @Mapped(() => ProductOutputDto)
  @Type(() => ProductOutputDto)
  @ApiProperty()
  product: ProductOutputDto;

  @Mapped(() => PaymentOutputDto)
  @Type(() => PaymentOutputDto)
  @ApiProperty()
  payment: PaymentOutputDto;
}
