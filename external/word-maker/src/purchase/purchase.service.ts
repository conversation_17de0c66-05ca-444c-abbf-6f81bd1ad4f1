import { BadRequestException, Injectable } from '@nestjs/common';
import { BazarService } from '../base/webServices/bazar/bazar.service';
import { PurchaseInputDto } from './purchase.dto';
import { bazar } from '../base/database/entities/bazar.entity';
import { PurchasesRepository } from './purchase.repository';
import {
  PaymentFromEnum,
  PaymentStateEnum,
  PurchaseStateEnum,
} from '../base/enums/purchase-state.enum';
import { LogService } from '../base/logger/log.service';
import { HttpResponseDto } from '../base/dto/general.dto';
import { HttpService } from '@nestjs/axios';
import { ProductRepository } from '../product/product.repository';
import * as ZarinpalCheckout from 'zarinpal-checkout';
import { PaymentsRepository } from '../payment/payment.repository';
import { DiscountService } from 'src/discount/discount.service';
@Injectable()
export class PurchaseService {
  constructor(
    private readonly bazarService: BazarService,
    private readonly purchasesRepository: PurchasesRepository,
    private readonly productsRepository: ProductRepository,
    private readonly paymentsRepository: PaymentsRepository,
    private readonly discountService: DiscountService,

    private readonly logService: LogService,
    private readonly httpService: HttpService,
  ) {}

  async verifyPurchase(
    userId: number,
    purchaseInputDto: PurchaseInputDto,
    username: string,
  ): Promise<HttpResponseDto> {
    // const whiteUsers = ['9155528224', '9113331643', '9396186896', '9368014847'];
    // if (purchaseInputDto.product_id === 3 && !whiteUsers.includes(username))
    //   throw new BadRequestException('Invalid product_id');
    switch (purchaseInputDto.payment_from) {
      case PaymentFromEnum.BAZAR:
        return await this.verifyPurchaseBazar(userId, purchaseInputDto);
      case PaymentFromEnum.SIBAPP:
        return await this.verifyPurchaseSibApp(userId, purchaseInputDto);
      case PaymentFromEnum.ZARINPAL:
        return await this.verifyPurchaseZarinpal(userId, purchaseInputDto);
    }
  }

  async verifyPurchaseBazar(
    userId: number,
    purchaseInputDto: PurchaseInputDto,
  ): Promise<HttpResponseDto> {
    const token = await this.bazarService.getLastToken();
    const access_token = await this.getAccessToken(token);

    let result;
    // get purchase status from bazar web service
    result = await this.bazarService.verifyPurchase(
      purchaseInputDto,
      access_token,
    );
    if (result === '401') {
      const access_token = await this.getAccessToken(token);
      result = await this.bazarService.verifyPurchase(
        purchaseInputDto,
        access_token,
      );
    }
    // save in db
    if (result != '401' && result.kind) {
      try {
        await this.purchasesRepository.save({
          user_id: userId,
          consumptionState: result.consumptionState,
          packageName: purchaseInputDto.package_name,
          product_id: purchaseInputDto.product_id,
          time: new Date(result.purchaseTime),
          state:
            result.purchaseState == 0
              ? PurchaseStateEnum.PURCHASED
              : PurchaseStateEnum.REFUNDED,
          purchaseTime: result.purchaseTime,
          payload: result.developerPayload,
          orderId: purchaseInputDto.order_id,
          token: purchaseInputDto.purchase_token,
        });
        result = { status: 200, data: result };
      } catch (e) {
        result = {
          status: 412,
          message: 'توکن با این مشخصات قبلا در دیتابیس ذخیره شده است',
          data: result,
        };
        this.logService.createLog('save new purchase', e);
      }
    } else {
      result = { status: 500, data: result };
    }
    return result;
  }

  async getPurchases(userId: number) {
    const purchases = await this.purchasesRepository.getByUserId(userId);
    return purchases;
  }

  private async getAccessToken(token: bazar) {
    let access_token = '';
    if (token) {
      access_token = token.access_token;
      const currentDate = new Date();
      const diffInDays =
        (currentDate.getTime() - token.updated_at.getTime()) /
        (1000 * 3600 * 24);
      if (diffInDays < 40)
        access_token = await this.bazarService.getNewAccessToken(
          token,
          diffInDays,
        );
      else access_token = await this.bazarService.loginToBazar();
    } else access_token = await this.bazarService.loginToBazar();
    return access_token;
  }

  async verifyPurchaseSibApp(
    userId: number,
    purchaseInputDto: PurchaseInputDto,
  ): Promise<HttpResponseDto> {
    const applicationToken = process.env.AIB_APP_TOKEN; // باید application_token را از config یا environment بگیرید

    try {
      const url = `https://api.sibapp.net/api/v1/purchase/${purchaseInputDto.purchase_token}/validate`;

      const response = await this.httpService.axiosRef.get(url, {
        headers: {
          'Content-Type': 'application/json',
          'Secret-Key': applicationToken,
        },
      });
      let result = response.data;
      if (result) {
        try {
          const product = await this.productsRepository.getBySibAppId(
            result.target.package.id,
          );
          if (!product) throw new Error('Error Product id');
          await this.purchasesRepository.save({
            user_id: userId,
            packageName: result.target.package.name,
            product_id: product.id,
            time: new Date(result.create_time),
            state: PurchaseStateEnum.PURCHASED,
            purchaseTime: result.create_time,
            payload: result.developer_payload,
            token: purchaseInputDto.purchase_token,
          });
          result = { status: 200, data: result };
        } catch (e) {
          result = {
            status: 412,
            message: 'توکن با این مشخصات قبلا در دیتابیس ذخیره شده است',
            data: result,
          };
          this.logService.createLog('save new purchase', e);
        }
      } else {
        result = { status: 500, data: result };
      }
      return result;
    } catch (error) {
      this.logService.createLog('Error verifying purchase', error);
      throw new Error('Error verifying purchase');
    }
  }

  async verifyPurchaseZarinpal(
    userId: number,
    purchaseInputDto: PurchaseInputDto,
  ): Promise<HttpResponseDto> {
    let result;
    const zarinpal = ZarinpalCheckout.create(
      process.env.ZARIN_MERCHANT_ID,
      false,
    );
    try {
      const payment = await this.paymentsRepository.findOneByOrderId(
        purchaseInputDto.order_id,
      );
      if (!payment) {
        throw new Error('Payment not found.');
      }

      const verificationResult = await zarinpal.PaymentVerification({
        Amount: Number(payment.final_price) / 10,
        Authority: purchaseInputDto.purchase_token,
      });

      // save in db
      if (
        verificationResult.status === 100 ||
        verificationResult.status === 101
      ) {
        try {
          payment.state = PaymentStateEnum.SUCCESS;
          await this.paymentsRepository.update(payment);
          const purchase = await this.purchasesRepository.getByOrderId(
            purchaseInputDto.order_id,
          );
          if (purchase) {
            purchase.state = PurchaseStateEnum.PURCHASED;
            purchase.payment_id = payment.id;
            purchase.payment = payment;
            await this.purchasesRepository.save(purchase);
            return {
              status: 200,
              message: 'Purchase already exists.',
              data: purchase,
            };
          }
          await this.purchasesRepository.save({
            user_id: userId,
            packageName: purchaseInputDto.package_name,
            product_id: payment.product_id,
            time: new Date(),
            state: PurchaseStateEnum.PURCHASED,
            payload: purchaseInputDto.price,
            orderId: purchaseInputDto.order_id,
            token: purchaseInputDto.purchase_token,
            payment_id: payment.id,
          });
          // Consume discount if used
          if (payment.discount_id) {
            try {
              // Get discount by ID
              const discount = await this.discountService.getDiscountById(
                payment.discount_id,
              );

              if (discount) {
                await this.discountService.consumeDiscountForPayment(
                  discount.code,
                  payment.id,
                );
              }
            } catch (error) {
              this.logService.createLog(
                'Failed to consume discount after successful payment',
                error,
              );
              // Don't fail the payment if discount consumption fails
            }
          }

          result = { status: 200, data: result };
        } catch (e) {
          result = {
            status: 412,
            message: 'توکن با این مشخصات قبلا در دیتابیس ذخیره شده است',
            data: result,
          };
          this.logService.createLog('save new purchase', e);
        }
      } else {
        payment.state = PaymentStateEnum.UNSUCCESS;
        await this.paymentsRepository.update(payment);

        // If payment failed and discount was reserved, rollback
        if (payment.discount_id) {
          try {
            await this.discountService.rollbackDiscountConsumption(
              payment.discount_id,
            );
          } catch (error) {
            this.logService.createLog(
              'Failed to rollback discount after failed payment',
              error,
            );
          }
        }

        result = {
          status: 500,
          data: { success: false, message: 'پرداخت موفق نبود.' },
        };
      }
    } catch (error) {
      this.logService.createLog('error', error);
      result = { status: 500, data: error.response };
    }
    return result;
  }
}
