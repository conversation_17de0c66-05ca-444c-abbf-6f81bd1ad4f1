import {
  Body,
  Controller,
  Post,
  UseGuards,
  Request,
  Get,
  UseInterceptors,
  Res,
} from '@nestjs/common';
import { PurchaseService } from './purchase.service';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AuthGuard } from '../auth/auth.guard';
import { PurchaseInputDto, PurchasesOutputDto } from './purchase.dto';
import { MapInterceptor } from '../base/utils/mapper/mapper.interceptor';
import { HttpResponseDto } from '../base/dto/general.dto';
import { Response } from 'express';
import { PaymentFromEnum } from '../base/enums/purchase-state.enum';
@Controller('purchase')
@UseGuards(AuthGuard)
@ApiTags('Purchase')
@ApiBearerAuth()
export class PurchaseController {
  constructor(private purchaseService: PurchaseService) {}

  @Post('/verifyPurchase')
  @ApiResponse({
    status: 200,
    description: 'اطلاعات پرداخت کاربر با موفقیت ثبت شد',
    type: HttpResponseDto,
  })
  async verifyPurchase(
    @Body() purchaseInputDto: PurchaseInputDto,
    @Request() req,
    @Res() res: Response,
  ): Promise<void> {
    if (!purchaseInputDto.payment_from)
      purchaseInputDto.payment_from = PaymentFromEnum.BAZAR;
    const result = await this.purchaseService.verifyPurchase(
      req.user.id,
      purchaseInputDto,
      req.user.username,
    );
    res.status(result.status).json(result);
  }

  @Get('purchases')
  @ApiResponse({
    status: 200,
    description: 'اطلاعات پرداخت کاربر',
    type: PurchasesOutputDto,
  })
  @UseInterceptors(MapInterceptor(PurchasesOutputDto))
  async getPurchases(@Request() req) {
    const userInfo = await this.purchaseService.getPurchases(req.user.id);
    return userInfo;
  }
}
