import {
  BadRequestException,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import 'dotenv/config';
import { UsersRepository } from './user.repository';
import { UserCompletionInputDto, UserOutputDto } from './users.dto';
import { HttpResponseDto } from '../base/dto/general.dto';
import { convertToHttpResponseDto } from '../base/utils/convert-info';
import { VerifiedByEnum } from '../base/enums/user.enums';

@Injectable()
export class UsersService {
  constructor(private readonly usersRepository: UsersRepository) {}
  async getUser(userId: number): Promise<UserOutputDto> {
    try {
      const user = await this.usersRepository.findOneById(userId);
      return user;
    } catch {
      throw new UnauthorizedException();
    }
  }
  async completeInfo(
    userCompletionInputDto: UserCompletionInputDto,
    userId: number,
  ): Promise<HttpResponseDto> {
    if (userCompletionInputDto.email || userCompletionInputDto.phone) {
      const { phone, email, country_code } = userCompletionInputDto;
      const user = await this.usersRepository.findOneById(userId);
      if (user.verified_by === VerifiedByEnum.EMAIL && email)
        throw new BadRequestException(
          'اجازه ویرایش ایمیل کاربری که با ایمیل وریفای شده را ندارید',
        );
      else if (
        user.verified_by === VerifiedByEnum.PHONE &&
        (phone || country_code)
      )
        throw new BadRequestException(
          'اجازه ویرایش شماره تماس کاربری که با شماره تماس وریفای شده را ندارید',
        );
      if (userCompletionInputDto.phone) {
        const found = await this.usersRepository.checkExistByPhone(
          phone,
          country_code,
        );
        if (found && found.id != userId)
          throw new BadRequestException('شماره تماس وارد شده تکراری می باشد');
      }
      if (userCompletionInputDto.email) {
        const found = await this.usersRepository.checkExistByEmail(email);
        if (found && found.id != userId)
          throw new BadRequestException('ایمیل وارد شده تکراری می باشد');
      }
    }
    return convertToHttpResponseDto(
      await this.usersRepository.updateInfo(userId, userCompletionInputDto),
    );
  }
}
