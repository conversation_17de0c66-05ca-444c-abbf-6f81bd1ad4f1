import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { users } from '../base/database/entities/users.entity';
import { UserRegistrationInputDto } from '../auth/auth.dto';
import { UserCompletionInputDto } from './users.dto';
import { devices } from '../base/database/entities/devices.entity';
import { UpdateUserTypeInputDto, UserSearchDto } from '../admin/admin.dto';
import { UserTypeEnum } from '../base/enums/user.enums';

@Injectable()
export class UsersRepository {
  constructor(
    @InjectRepository(users)
    private readonly repository: Repository<users>,
    @InjectRepository(devices)
    private readonly deviceRepository: Repository<devices>,
  ) {}

  async login(country_code: string, phone: string): Promise<users> {
    return this.repository.findOne({ where: { phone, country_code } });
  }

  async findOneById(id: number): Promise<users> {
    return this.repository.findOne({
      where: { id },
    });
  }
  async checkExistByEmail(email: string): Promise<users> {
    return this.repository.findOne({
      where: { email },
    });
  }
  async checkExistByPhone(phone: string, country_code: string): Promise<users> {
    return this.repository.findOne({
      where: { phone, country_code },
    });
  }
  async checkExist(
    phone: string,
    country_code: string,
    email: string,
  ): Promise<users> {
    return this.repository.findOne({
      where: { phone, country_code, email },
    });
  }
  async save(dto: UserRegistrationInputDto): Promise<users> {
    const result = await this.repository.save(dto);
    return result;
  }
  async updateConfirmationCode(userId: number, confirmation_code: string) {
    const result = await this.repository.update(userId, { confirmation_code });
    return result;
  }
  async updateInfo(
    userId: number,
    userCompletionInputDto: UserCompletionInputDto,
  ) {
    const result = await this.repository.update(userId, {
      ...userCompletionInputDto,
    });
    return result;
  }

  async updateRefreshToken(
    device_id: number,
    refresh_token: string,
  ): Promise<void> {
    await this.deviceRepository.update({ id: device_id }, { refresh_token });
  }

  async findDeviceByIdAndRefreshToken(
    user_id: number,
    refresh_token: string,
  ): Promise<devices> {
    return this.deviceRepository.findOne({
      where: { user_id, refresh_token },
      relations: { user: true },
    });
  }

  async findDeviceByUserId(user_id: number): Promise<devices[]> {
    return this.deviceRepository.find({
      where: { user_id },
      order: { id: 'ASC' },
    });
  }
  async removeDevice(device_id: number) {
    return this.deviceRepository.delete({ id: device_id });
  }
  async saveDevice(user_id: number, device_id: string, refresh_token: string) {
    const device = await this.deviceRepository.findOne({
      where: { user_id, device_id },
    });
    if (device) this.updateRefreshToken(device.id, refresh_token);
    else {
      const devices = await this.findDeviceByUserId(user_id);
      if (devices && devices.length >= 2)
        await this.removeDevice(devices[0].id);
      await this.deviceRepository.save({
        device_id,
        user_id,
        refresh_token,
      });
    }
  }
  async get(userSearchDto: UserSearchDto): Promise<users[]> {
    const where: any = {};
    if (
      userSearchDto.phone !== null &&
      userSearchDto.phone !== undefined &&
      userSearchDto.phone !== ''
    ) {
      where.phone = userSearchDto.phone;
    }
    if (
      userSearchDto.email !== null &&
      userSearchDto.email !== undefined &&
      userSearchDto.email !== ''
    ) {
      where.email = userSearchDto.email;
    }
    if (
      userSearchDto.userType !== null &&
      userSearchDto.userType !== undefined
    ) {
      where.userType = userSearchDto.userType;
    }
    const result = await this.repository.find({ where });
    return result;
  }

  async updateUserTypeInfo(userId: number, userType: UserTypeEnum) {
    const result = await this.repository.update(userId, {
      userType,
    });
    return result;
  }
}
