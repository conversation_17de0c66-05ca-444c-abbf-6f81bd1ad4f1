import { ApiProperty } from '@nestjs/swagger';
import { users } from '../base/database/entities/users.entity';
import { Mapped } from '../base/utils/mapper/mapper.decorator';
import { MappedWithEntity } from '../base/utils/mapper/mapper.types';
import {
  IsDateString,
  IsEmail,
  IsEnum,
  IsNotEmpty,
  IsOptional,
  Matches,
} from 'class-validator';
import {
  GenderEnum,
  RoleEnum,
  UserStatusEnum,
  UserTypeEnum,
  VerifiedByEnum,
} from '../base/enums/user.enums';

export class UserOutputDto implements MappedWithEntity<users> {
  @ApiProperty({ example: 1, description: 'شناسه کاربر' })
  @Mapped()
  id: number;

  @ApiProperty({ example: 'John', description: 'نام کاربر' })
  @Mapped()
  name?: string;

  @ApiProperty({ example: 'Doe', description: 'نام خانوادگی کاربر' })
  @Mapped()
  family?: string;

  @ApiProperty({ example: '+98', description: 'کد کشور' })
  @Mapped()
  country_code: string;

  @ApiProperty({ example: 'IR', description: 'نام کشور' })
  @Mapped()
  country_name: string;

  @ApiProperty({ example: '9123456789', description: 'شماره تلفن' })
  @Mapped()
  phone: string;

  @ApiProperty({ example: '<EMAIL>', description: 'ایمیل' })
  @Mapped()
  email?: string;

  @ApiProperty({
    example: '1990-01-01',
    description: 'تاریخ تولد به فرمت YYYY-MM-DD',
  })
  @Mapped()
  birth_date: string;

  @ApiProperty({
    example: 'female',
    description: 'جنسیت کاربر',
    enum: GenderEnum,
  })
  @Mapped()
  gender: GenderEnum;

  @ApiProperty({
    example: 'general',
    description: 'نوع کاربری',
    enum: UserTypeEnum,
  })
  @Mapped()
  userType: UserTypeEnum;

  @ApiProperty({
    example: 'PHONE',
    description: 'احراز هویت کاربر',
    enum: VerifiedByEnum,
  })
  @Mapped()
  verified_by?: VerifiedByEnum;

  @ApiProperty({
    example: 'Active',
    description: 'وضعیت کاربر',
    enum: UserStatusEnum,
  })
  @Mapped()
  status: UserStatusEnum;

  @ApiProperty({
    example: ['USER'],
    description: 'نقش‌های کاربر',
    enum: RoleEnum,
    isArray: true,
  })
  @Mapped()
  roles: RoleEnum[];

  @ApiProperty({ example: '2023-01-01T00:00:00Z', description: 'تاریخ ایجاد' })
  @Mapped()
  created_at: Date;
}

export class UserCompletionInputDto {
  @ApiProperty({ example: 'IR', description: 'نام کشور' })
  country_name: string;

  @Matches(/^\+\d{1,3}$/, {
    message: 'کد کشور باید با + شروع شود و شامل 1 تا 3 عدد باشد',
  })
  @IsOptional()
  @ApiProperty({ example: '+98', description: 'کد کشور' })
  country_code: string;

  @Matches(/^\d{9,10}$/, {
    message: 'شماره تلفن معتبر نیست. باید 9 یا 10 رقمی باشد',
  })
  @IsOptional()
  @ApiProperty({ example: '9121111111', description: 'شماره تلفن ' })
  phone: string;

  @ApiProperty({ example: 'name', description: 'نام' })
  @IsOptional()
  name: string;

  @ApiProperty({ example: 'family', description: 'نام خانوادگی' })
  @IsOptional()
  family: string;

  @IsOptional()
  @ApiProperty({ example: 'email', description: 'ایمیل' })
  @IsEmail({}, { message: 'ایمیل معتبر نیست' })
  email: string;

  @IsDateString({}, { message: 'تاریخ تولد معتبر نیست' })
  @ApiProperty({
    example: '1990-01-01',
    description: 'تاریخ تولد به فرمت YYYY-MM-DD',
  })
  @IsOptional()
  birth_date: string;

  @IsEnum(GenderEnum, {
    message: 'جنسیت کاربر باید یکی از مقادیر MALE یا FEMALE باشد',
  })
  @ApiProperty({ example: 'FEMALE', description: 'جنسیت کاربر' })
  @IsOptional()
  gender: GenderEnum;
}
