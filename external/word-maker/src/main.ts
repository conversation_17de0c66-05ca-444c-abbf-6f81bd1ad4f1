import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { BadRequestException, ValidationPipe } from '@nestjs/common';
import { CustomLogger } from './base/logger/custom-logger';
import { LogService } from './base/logger/log.service';
import { AllExceptionsFilter } from './base/logger/exception-filter';
require('dotenv').config({
  path: process.env.NODE_ENV === 'development' ? '.env.development' : '.env',
  override: true,
});
async function bootstrap() {
  const app = await NestFactory.create(AppModule); //, { httpsOptions }

  if (process.env.NODE_ENV !== 'development') {
    // log errors
    const logger = app.get(CustomLogger);
    app.useLogger(logger);
  }

  // log exception
  const logService = app.get(LogService);
  app.useGlobalFilters(new AllExceptionsFilter(logService));

  app.useGlobalPipes(
    new ValidationPipe({
      forbidNonWhitelisted: true,
      exceptionFactory: (errors) =>
        new BadRequestException(
          errors.map((error) => ({
            property: {
              [error.property]: Object.keys(error.constraints)[0],
            },
            message: error.constraints[Object.keys(error.constraints)[0]],
          })),
        ),
    }),
  );
  app.enableCors({
    credentials: true,
    origin: '*',
  });
  const config = new DocumentBuilder()
    .setTitle('WordMakers Api')
    .setDescription('The WordMaker description')
    .setExternalDoc('OpenAPI Json Schema', '/api/docs-json')
    .addServer(process.env.LOCALHOST_URL, 'Local environment')
    .setVersion('1.0')
    .addBearerAuth()
    .build();
  const document = SwaggerModule.createDocument(app, config);
  SwaggerModule.setup('api/docs', app, document, {
    customCss: '.swagger-ui .topbar { display: none }',
    explorer: true,
    swaggerOptions: {
      persistAuthorization: true,
      syntaxHighlight: {
        theme: 'monokai',
      },
    },
  });
  await app.listen(3002, '0.0.0.0');
}
bootstrap();
