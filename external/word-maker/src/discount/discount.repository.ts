import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import {
  Repository,
  MoreThanOrEqual,
  LessThanOrEqual,
  Between,
  Like,
} from 'typeorm';
import {
  discounts,
  DiscountStateEnum,
} from '../base/database/entities/discounts.entity';
import {
  DiscountSaveDto,
  DiscountSearchDto,
  DiscountSearchWithPaginationDto,
  UpdateDiscountInputDto,
} from './discount.dto';

@Injectable()
export class DiscountRepository {
  constructor(
    @InjectRepository(discounts)
    private readonly repository: Repository<discounts>,
  ) {}
  async getByCode(code: string): Promise<discounts> {
    return await this.repository.findOne({
      where: { code },
      relations: ['referral', 'referral.user'],
      select: {
        referral: {
          id: true,
          created_at: true,
          updated_at: true,
          entity_type: true,
          registration_code: true,
          corporation_name: true,
          province_id: true,
          city_id: true,
          address: true,
          description: true,
          user_id: true,
          user: {
            id: true,
            name: true,
            family: true,
            phone: true,
            email: true,
            status: true,
          },
        },
      },
    });
  }

  async getById(id: number): Promise<discounts> {
    return await this.repository.findOne({
      where: { id },
      relations: ['referral', 'referral.user'],
      select: {
        referral: {
          id: true,
          created_at: true,
          updated_at: true,
          entity_type: true,
          registration_code: true,
          corporation_name: true,
          province_id: true,
          city_id: true,
          address: true,
          description: true,
          user_id: true,
          user: {
            id: true,
            name: true,
            family: true,
            phone: true,
            email: true,
            status: true,
          },
        },
      },
    });
  }
  async get(
    discountSearchDto: DiscountSearchWithPaginationDto,
  ): Promise<{ data: discounts[]; total: number }> {
    const where: any = {};

    if (
      discountSearchDto.state !== null &&
      discountSearchDto.state !== undefined
    ) {
      where.state = discountSearchDto.state;
    }
    if (
      discountSearchDto.type !== null &&
      discountSearchDto.type !== undefined
    ) {
      where.type = discountSearchDto.type;
    }
    if (
      discountSearchDto.referral_id !== null &&
      discountSearchDto.referral_id !== undefined
    ) {
      where.referral_id = discountSearchDto.referral_id;
    }
    if (
      discountSearchDto.description !== null &&
      discountSearchDto.description !== undefined &&
      discountSearchDto.description !== ''
    ) {
      where.description = discountSearchDto.description;
    }
    if (
      discountSearchDto.code !== null &&
      discountSearchDto.code !== undefined &&
      discountSearchDto.code !== ''
    ) {
      where.code = Like(`%${discountSearchDto.code}%`);
    }
    if (
      discountSearchDto.created_date_from !== null &&
      discountSearchDto.created_date_from !== undefined &&
      discountSearchDto.created_date_from !== ''
    ) {
      if (
        discountSearchDto.created_date_to !== null &&
        discountSearchDto.created_date_to !== undefined &&
        discountSearchDto.created_date_to !== ''
      ) {
        // Both from and to provided
        where.created_at = Between(
          new Date(discountSearchDto.created_date_from),
          new Date(discountSearchDto.created_date_to + ' 23:59:59'),
        );
      } else {
        // Only from provided
        where.created_at = MoreThanOrEqual(
          new Date(discountSearchDto.created_date_from),
        );
      }
    } else if (
      discountSearchDto.created_date_to !== null &&
      discountSearchDto.created_date_to !== undefined &&
      discountSearchDto.created_date_to !== ''
    ) {
      // Only to provided
      where.created_at = LessThanOrEqual(
        new Date(discountSearchDto.created_date_to + ' 23:59:59'),
      );
    }

    if (
      discountSearchDto.expire_date_from !== null &&
      discountSearchDto.expire_date_from !== undefined &&
      discountSearchDto.expire_date_from !== ''
    ) {
      if (
        discountSearchDto.expire_date_to !== null &&
        discountSearchDto.expire_date_to !== undefined &&
        discountSearchDto.expire_date_to !== ''
      ) {
        // Both from and to provided
        where.expire_date = Between(
          new Date(discountSearchDto.expire_date_from),
          new Date(discountSearchDto.expire_date_to + ' 23:59:59'),
        );
      } else {
        // Only from provided
        where.expire_date = MoreThanOrEqual(
          new Date(discountSearchDto.expire_date_from),
        );
      }
    } else if (
      discountSearchDto.expire_date_to !== null &&
      discountSearchDto.expire_date_to !== undefined &&
      discountSearchDto.expire_date_to !== ''
    ) {
      // Only to provided
      where.expire_date = LessThanOrEqual(
        new Date(discountSearchDto.expire_date_to + ' 23:59:59'),
      );
    }

    const page = discountSearchDto.page || 1;
    const limit = discountSearchDto.limit || 10;
    const skip = (page - 1) * limit;

    const [data, total] = await this.repository.findAndCount({
      where,
      skip,
      select: {
        referral: {
          id: true,
          created_at: true,
          updated_at: true,
          entity_type: true,
          registration_code: true,
          corporation_name: true,
          province_id: false,
          city_id: false,
          address: true,
          description: true,
          user_id: false,
          province: {
            id: true,
            title: true,
          },
          city: {
            id: true,
            title: true,
          },
          user: {
            id: true,
            name: true,
            family: true,
            phone: true,
            email: true,
            status: true,
          },
        },
      },
      take: limit,
      order: { created_at: 'DESC' }, // Order by creation date, newest first
      relations: [
        'referral',
        'referral.user',
        'referral.province',
        'referral.city',
      ],
    });

    return { data, total };
  }

  async getAllForExport(
    discountSearchDto: DiscountSearchDto,
  ): Promise<discounts[]> {
    const where: any = {};

    if (
      discountSearchDto.state !== null &&
      discountSearchDto.state !== undefined
    ) {
      where.state = discountSearchDto.state;
    }
    if (
      discountSearchDto.type !== null &&
      discountSearchDto.type !== undefined
    ) {
      where.type = discountSearchDto.type;
    }
    if (
      discountSearchDto.referral_id !== null &&
      discountSearchDto.referral_id !== undefined
    ) {
      where.referral_id = discountSearchDto.referral_id;
    }
    if (
      discountSearchDto.description !== null &&
      discountSearchDto.description !== undefined &&
      discountSearchDto.description !== ''
    ) {
      where.description = discountSearchDto.description;
    }
    if (
      discountSearchDto.code !== null &&
      discountSearchDto.code !== undefined &&
      discountSearchDto.code !== ''
    ) {
      where.code = Like(`%${discountSearchDto.code}%`);
    }
    if (
      discountSearchDto.created_date_from !== null &&
      discountSearchDto.created_date_from !== undefined &&
      discountSearchDto.created_date_from !== ''
    ) {
      if (
        discountSearchDto.created_date_to !== null &&
        discountSearchDto.created_date_to !== undefined &&
        discountSearchDto.created_date_to !== ''
      ) {
        // Both from and to provided
        where.created_at = Between(
          new Date(discountSearchDto.created_date_from),
          new Date(discountSearchDto.created_date_to + ' 23:59:59'),
        );
      } else {
        // Only from provided
        where.created_at = MoreThanOrEqual(
          new Date(discountSearchDto.created_date_from),
        );
      }
    } else if (
      discountSearchDto.created_date_to !== null &&
      discountSearchDto.created_date_to !== undefined &&
      discountSearchDto.created_date_to !== ''
    ) {
      // Only to provided
      where.created_at = LessThanOrEqual(
        new Date(discountSearchDto.created_date_to + ' 23:59:59'),
      );
    }

    if (
      discountSearchDto.expire_date_from !== null &&
      discountSearchDto.expire_date_from !== undefined &&
      discountSearchDto.expire_date_from !== ''
    ) {
      if (
        discountSearchDto.expire_date_to !== null &&
        discountSearchDto.expire_date_to !== undefined &&
        discountSearchDto.expire_date_to !== ''
      ) {
        // Both from and to provided
        where.expire_date = Between(
          new Date(discountSearchDto.expire_date_from),
          new Date(discountSearchDto.expire_date_to + ' 23:59:59'),
        );
      } else {
        // Only from provided
        where.expire_date = MoreThanOrEqual(
          new Date(discountSearchDto.expire_date_from),
        );
      }
    } else if (
      discountSearchDto.expire_date_to !== null &&
      discountSearchDto.expire_date_to !== undefined &&
      discountSearchDto.expire_date_to !== ''
    ) {
      // Only to provided
      where.expire_date = LessThanOrEqual(
        new Date(discountSearchDto.expire_date_to + ' 23:59:59'),
      );
    }

    const data = await this.repository.find({
      where,
      order: { created_at: 'DESC' }, // Order by creation date, newest first
      relations: ['referral', 'referral.user'],
      select: {
        id: true,
        created_at: true,
        updated_at: true,
        code: true,
        discount_percent: true,
        expire_date: true,
        state: true,
        type: true,
        referral_id: true,
        description: true,
        payment_id: true,
        referral: {
          id: true,
          created_at: true,
          updated_at: true,
          entity_type: true,
          registration_code: true,
          corporation_name: true,
          province_id: true,
          city_id: true,
          address: true,
          description: true,
          user_id: true,
          user: {
            id: true,
            name: true,
            family: true,
            phone: true,
            email: true,
            status: true,
          },
        },
      },
    });

    return data;
  }

  async save(dto: DiscountSaveDto[]): Promise<discounts[]> {
    const result = await this.repository.save(dto);
    return result;
  }
  async updateStatus(dto: UpdateDiscountInputDto) {
    const discount = await this.repository.findOneBy({ id: dto.id });

    if (discount) {
      discount.state = dto.state;
      discount.payment_id = dto.paymentId;
      const result = await this.repository.save(discount);
      return result;
    }
    return null;
  }

  async getDiscountCounts(): Promise<{
    activeCount: number;
    usedCount: number;
    expiredCount: number;
  }> {
    const now = new Date();

    const [activeResult, usedResult, expiredResult] = await Promise.all([
      this.repository.count({ where: { state: DiscountStateEnum.ACTIVE } }),
      this.repository.count({ where: { state: DiscountStateEnum.USED } }),
      this.repository.count({
        where: {
          expire_date: LessThanOrEqual(now),
          state: DiscountStateEnum.ACTIVE,
        },
      }),
    ]);

    return {
      activeCount: activeResult,
      usedCount: usedResult,
      expiredCount: expiredResult,
    };
  }
}
