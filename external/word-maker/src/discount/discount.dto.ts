import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  Min,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { MappedWithEntity } from '../base/utils/mapper/mapper.types';
import {
  discounts,
  DiscountStateEnum,
  DiscountTypeEnum,
} from '../base/database/entities/discounts.entity';
import { Mapped } from '../base/utils/mapper/mapper.decorator';
import {
  AffiliateResponseDto,
  mapToAffiliateResponseDto,
} from 'src/affiliate/affiliate.dto';

export class DiscountSearchDto {
  @IsEnum(DiscountStateEnum, {
    message:
      'وضعیت کد تخفیف باید یکی از مقادیر ACTIVE یا DEACTIVE یا USED یا UNUSED باشد',
  })
  @ApiPropertyOptional({
    enum: DiscountStateEnum,
    example: 'ACTIVE',
    description: 'وضعیت کد تخفیف',
  })
  @IsOptional()
  state?: DiscountStateEnum;

  @IsEnum(DiscountTypeEnum)
  @ApiPropertyOptional({
    enum: DiscountTypeEnum,
    example: 'simple',
    description: 'نوع کد تخفیف',
  })
  @IsOptional()
  @Mapped()
  type?: DiscountTypeEnum;

  @ApiPropertyOptional()
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  @Mapped()
  referral_id?: number;

  @ApiPropertyOptional()
  @IsOptional()
  @IsString()
  @Mapped()
  description?: string;

  @ApiPropertyOptional()
  @IsOptional()
  @Mapped()
  code?: string;

  @ApiPropertyOptional({
    description: 'Created date from (YYYY-MM-DD)',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDateString()
  created_date_from?: string;

  @ApiPropertyOptional({
    description: 'Created date to (YYYY-MM-DD)',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsDateString()
  created_date_to?: string;

  @ApiPropertyOptional({
    description: 'Expire date from (YYYY-MM-DD)',
    example: '2023-01-01',
  })
  @IsOptional()
  @IsDateString()
  expire_date_from?: string;

  @ApiPropertyOptional({
    description: 'Expire date to (YYYY-MM-DD)',
    example: '2023-12-31',
  })
  @IsOptional()
  @IsDateString()
  expire_date_to?: string;
}

export class DiscountSearchWithPaginationDto extends DiscountSearchDto {
  @ApiPropertyOptional({
    description: 'Page number for pagination (starts from 1)',
    example: 1,
    default: 1,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({
    description: 'Number of items per page',
    example: 10,
    default: 10,
  })
  @IsOptional()
  @IsNumber()
  @Type(() => Number)
  limit?: number = 10;
}

export class DiscountOutputDto implements MappedWithEntity<discounts> {
  @ApiProperty()
  @Mapped()
  id: number;

  @ApiProperty()
  @Mapped()
  code: string;

  @ApiProperty({ description: 'درصد تخفیف' })
  @Mapped()
  @Min(0)
  @Max(100)
  discount_percent: number;

  @ApiProperty({ description: 'تاریخ انقضا' })
  @Mapped()
  expire_date?: Date;

  @ApiProperty({
    example: 'active',
    description: 'وضعیت کد تخفیف ',
    enum: DiscountStateEnum,
  })
  @Mapped()
  state?: DiscountStateEnum;

  @ApiProperty({
    example: 'simple',
    description: 'نوع کد تخفیف',
    enum: DiscountTypeEnum,
  })
  @Mapped()
  type?: DiscountTypeEnum;

  @ApiProperty({ description: 'توضیحات' })
  @Mapped()
  description?: string;

  @ApiProperty({
    type: AffiliateResponseDto,
    description: 'اطلاعات همکار ارجاع‌دهنده',
    required: false,
  })
  @Mapped()
  referral?: AffiliateResponseDto;

  @ApiPropertyOptional({
    description: 'Commission percentage for affiliate/referral (0-100)',
    example: 15,
    required: false,
  })
  @Mapped()
  commission_percent?: number;
}

export class DiscountInputDto {
  @ApiProperty({ example: 'YYYY-MM-DD', description: 'تاریخ انقضای کد تخفیف' })
  @IsOptional()
  @IsDateString()
  expire_date: string;

  @ApiProperty({ example: 'ACTIVE', description: 'وضعیت کد تخفیف ' })
  @IsString()
  @IsOptional()
  prefix?: string;

  @ApiProperty()
  @IsNumber()
  @Min(0)
  @Max(100)
  discount_percent: number;

  @ApiProperty()
  @IsNumber()
  count: number;

  @IsEnum(DiscountTypeEnum)
  @ApiProperty({
    example: 'simple',
    description: 'نوع کد تخفیف',
    enum: DiscountTypeEnum,
  })
  @IsOptional()
  type?: DiscountTypeEnum;

  @ApiProperty({ description: 'شناسه ارجاع' })
  @IsNumber()
  @IsOptional()
  referral_id?: number;

  @ApiProperty({ description: 'توضیحات' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: 'Commission percentage for affiliate/referral (0-100)',
    example: 15,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  commission_percent?: number;
}

export class DiscountSaveDto {
  @ApiProperty()
  @IsOptional()
  expire_date?: string;

  @ApiProperty({ example: '10', description: 'درصد تخفیف' })
  @IsNumber()
  @Min(0)
  @Max(100)
  discount_percent: number;

  @IsEnum(DiscountStateEnum)
  @ApiProperty({ example: 'active', description: 'وضعیت' })
  @IsNotEmpty()
  state: DiscountStateEnum;

  @IsEnum(DiscountTypeEnum)
  @ApiProperty({
    example: 'simple',
    description: 'نوع کد تخفیف',
    enum: DiscountTypeEnum,
  })
  @IsOptional()
  type?: DiscountTypeEnum;

  @ApiProperty({ description: 'شناسه ارجاع' })
  @IsNumber()
  @IsOptional()
  referral_id?: number;

  @ApiProperty({ description: 'توضیحات' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ example: 'code', description: 'کد تخفیف' })
  @IsString()
  code: string;

  @ApiPropertyOptional({
    description: 'Commission percentage for affiliate/referral (0-100)',
    example: 15,
    required: false,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(100)
  commission_percent?: number;
}

export class UpdateDiscountInputDto {
  id: number;
  state?: DiscountStateEnum;
  paymentId: number;
}

export class ConsumeDiscountInputDto {
  @ApiProperty({ example: 'ABCD', description: 'کد تخفیف' })
  @IsString()
  @IsNotEmpty()
  code: string;
}

export class ConsumeDiscountOutputDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  discount?: DiscountOutputDto;
}

export class CheckDiscountInputDto {
  @ApiProperty({ example: 'ABCD12', description: 'کد تخفیف' })
  @IsString()
  @IsNotEmpty()
  code: string;
}

export class CheckDiscountOutputDto {
  @ApiProperty()
  success: boolean;

  @ApiProperty()
  message: string;

  @ApiProperty()
  discount?: DiscountOutputDto;
}

export class DiscountPaginationOutputDto {
  @ApiProperty({ type: [DiscountOutputDto] })
  data: DiscountOutputDto[];

  @ApiProperty()
  total: number;

  @ApiProperty()
  page: number;

  @ApiProperty()
  limit: number;

  @ApiProperty()
  totalPages: number;

  @ApiProperty({ description: 'Count of active discounts' })
  activeCount: number;

  @ApiProperty({ description: 'Count of used discounts' })
  usedCount: number;

  @ApiProperty({ description: 'Count of expired discounts' })
  expiredCount: number;
}

export function mapToDiscountOutputDto(discount: discounts): DiscountOutputDto {
  {
    return {
      id: discount.id,
      code: discount.code,
      discount_percent: discount.discount_percent,
      state: discount.state,
      type: discount.type,
      expire_date: discount.expire_date,
      description: discount.description,
      commission_percent: discount.commission_percent,
      referral: discount.referral
        ? mapToAffiliateResponseDto(discount.referral, discount.referral.user)
        : null,
    };
  }
}
