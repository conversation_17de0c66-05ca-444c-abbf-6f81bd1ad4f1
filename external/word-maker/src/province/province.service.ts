import { Injectable } from '@nestjs/common';
import { ProvinceRepository } from './province.repository';
import { ProvinceResponseDto, CityResponseDto } from './province.dto';

@Injectable()
export class ProvinceService {
  constructor(private readonly provinceRepository: ProvinceRepository) {}

  async getProvinces(): Promise<ProvinceResponseDto[]> {
    const provinces = await this.provinceRepository.find({
      where: { parent: 0 },
      order: { sort: 'ASC' },
    });

    return provinces.map((province) => ({
      id: province.id,
      title: province.title,
      sort: province.sort,
    }));
  }

  async getCitiesByProvince(provinceId: number): Promise<CityResponseDto[]> {
    const cities = await this.provinceRepository.find({
      where: { parent: provinceId },
      order: { title: 'ASC' },
    });

    return cities.map((city) => ({
      id: city.id,
      title: city.title,
      sort: city.sort,
      parent: city.parent,
    }));
  }
}
