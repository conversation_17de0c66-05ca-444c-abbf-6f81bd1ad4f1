import { Module } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ProvinceController } from './province.controller';
import { ProvinceService } from './province.service';
import { ProvinceRepository } from './province.repository';
import { province_cities } from '../base/database/entities/province-cities.entity';

@Module({
  imports: [TypeOrmModule.forFeature([province_cities])],
  controllers: [ProvinceController],
  providers: [ProvinceService, ProvinceRepository],
  exports: [ProvinceService, ProvinceRepository],
})
export class ProvinceModule {}
