import { Controller, Get, Param, ParseIntPipe } from '@nestjs/common';
import { ApiTags, ApiResponse, ApiParam, ApiOperation } from '@nestjs/swagger';
import { ProvinceService } from './province.service';
import { ProvinceResponseDto, CityResponseDto } from './province.dto';

@Controller('provinces')
@ApiTags('Provinces')
export class ProvinceController {
  constructor(private readonly provinceService: ProvinceService) {}

  @Get()
  @ApiOperation({
    summary: 'دریافت لیست استان‌ها',
    description: 'لیست تمامی استان‌ها را برمی‌گرداند',
  })
  @ApiResponse({
    status: 200,
    description: 'لیست استان‌ها با موفقیت بازیابی شد',
    type: [ProvinceResponseDto],
  })
  async getProvinces(): Promise<ProvinceResponseDto[]> {
    return this.provinceService.getProvinces();
  }

  @Get(':id/cities')
  @ApiOperation({
    summary: 'دریافت لیست شهرهای یک استان',
    description: 'لیست تمامی شهرهای یک استان را برمی‌گرداند',
  })
  @ApiParam({
    name: 'id',
    description: 'شناسه استان',
    type: 'number',
  })
  @ApiResponse({
    status: 200,
    description: 'لیست شهرهای استان با موفقیت بازیابی شد',
    type: [CityResponseDto],
  })
  @ApiResponse({
    status: 404,
    description: 'استان مورد نظر یافت نشد',
  })
  async getCitiesByProvince(
    @Param('id', ParseIntPipe) id: number,
  ): Promise<CityResponseDto[]> {
    return this.provinceService.getCitiesByProvince(id);
  }
}
