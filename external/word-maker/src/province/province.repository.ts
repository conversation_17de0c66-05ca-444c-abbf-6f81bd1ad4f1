import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { province_cities } from '../base/database/entities/province-cities.entity';

@Injectable()
export class ProvinceRepository extends Repository<province_cities> {
  constructor(
    @InjectRepository(province_cities)
    private readonly provinceCitiesRepository: Repository<province_cities>,
  ) {
    super(
      provinceCitiesRepository.target,
      provinceCitiesRepository.manager,
      provinceCitiesRepository.queryRunner,
    );
  }
}
