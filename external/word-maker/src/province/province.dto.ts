import { ApiProperty } from '@nestjs/swagger';
import { province_cities } from '../base/database/entities/province-cities.entity';
import { Mapped } from '../base/utils/mapper/mapper.decorator';
import { MappedWithEntity } from '../base/utils/mapper/mapper.types';

export class ProvinceResponseDto implements MappedWithEntity<province_cities> {
  @ApiProperty({ example: 1, description: 'شناسه استان' })
  @Mapped()
  id: number;

  @ApiProperty({ example: 'تهران', description: 'نام استان' })
  @Mapped()
  title: string;

  @ApiProperty({ example: 1, description: 'ترتیب نمایش' })
  @Mapped()
  sort: number;
}

export class CityResponseDto implements MappedWithEntity<province_cities> {
  @ApiProperty({ example: 45, description: 'شناسه شهر' })
  @Mapped()
  id: number;

  @ApiProperty({ example: 'تبریز', description: 'نام شهر' })
  @Mapped()
  title: string;

  @ApiProperty({ example: 2, description: 'ترتیب نمایش' })
  @Mapped()
  sort: number;

  @ApiProperty({ example: 8, description: 'شناسه استان مربوطه' })
  @Mapped()
  parent: number;
}
