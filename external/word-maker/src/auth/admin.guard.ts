import {
  CanActivate,
  ExecutionContext,
  Injectable,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { TokenService } from '../token/token.service';

@Injectable()
export class AdminGuard implements CanActivate {
  private readonly adminUsernames: string[];

  constructor(
    private jwtService: JwtService,
    private tokenService: TokenService,
  ) {
    this.adminUsernames = (process.env.ADMIN_USERNAMES || '')
      .split(',')
      .map((u) => u.trim());
  }

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = this.extractTokenFromHeader(request);
    if (!token || (await this.tokenService.isBlacklisted(token))) {
      throw new UnauthorizedException('unauthorized token');
    }
    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.SECRET_KEY,
      });

      // Check if user is admin using cached admin usernames
      if (!this.adminUsernames.includes(payload.username)) {
        throw new UnauthorizedException('شما مجاز به انجام این عملیات نیستید.');
      }

      request['user'] = payload;
    } catch (error) {
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new UnauthorizedException('unauthorized ' + request.url);
    }
    return true;
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
