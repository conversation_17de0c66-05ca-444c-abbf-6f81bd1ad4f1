import { ApiProperty } from '@nestjs/swagger';
import {
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsString,
  Length,
  Matches,
  MaxLength,
  MinLength,
} from 'class-validator';
import { MappedWithEntity } from '../base/utils/mapper/mapper.types';
import { users } from '../base/database/entities/users.entity';
import { Mapped } from '../base/utils/mapper/mapper.decorator';

export class UserRegistrationInputDto {
  @IsNotEmpty({ message: '  نام کشور نمی تواند خالی باشد' })
  @ApiProperty({ example: 'IR', description: 'نام کشور' })
  country_name: string;

  @Matches(/^\+\d{1,3}$/, {
    message: 'کد کشور باید با + شروع شود و شامل 1 تا 3 عدد باشد',
  })
  @IsNotEmpty({ message: '  کد کشور نمی تواند خالی باشد' })
  @ApiProperty({ example: '+98', description: 'کد کشور' })
  country_code: string;

  @Matches(/^\d{9,10}$/, {
    message: 'شماره تلفن معتبر نیست. باید 9 یا 10 رقمی باشد',
  })
  @IsNotEmpty({ message: ' شماره تلفن نمیتواند خالی باشد' })
  @ApiProperty({ example: '9121111111', description: 'شماره تلفن ' })
  phone: string;

  @IsOptional()
  @ApiProperty({ example: 'email', description: 'ایمیل' })
  @IsEmail({}, { message: 'ایمیل معتبر نیست' })
  email: string;
}

export class UserRegistrationOutputDto implements MappedWithEntity<users> {
  @ApiProperty()
  @Mapped()
  id: number;
  @ApiProperty()
  @Mapped()
  country_code: string;
  @ApiProperty()
  @Mapped()
  phone: string;
  @ApiProperty()
  @Mapped()
  email: string;
}
export class CheckRegisterPhoneInputDto {
  @IsNotEmpty({ message: '  نام کشور نمی تواند خالی باشد' })
  @ApiProperty({ example: 'IR', description: 'نام کشور' })
  country_name: string;

  @Matches(/^\+\d{1,3}$/, {
    message: 'کد کشور باید با + شروع شود و شامل 1 تا 3 عدد باشد',
  })
  @IsNotEmpty({ message: '  کد کشور نمی تواند خالی باشد' })
  @ApiProperty({ example: '+98', description: 'کد کشور' })
  country_code: string;

  @Matches(/^\d{9,10}$/, {
    message: 'شماره تلفن معتبر نیست. باید 9 یا 10 رقمی باشد',
  })
  @IsNotEmpty({ message: ' شماره تلفن نمیتواند خالی باشد' })
  @ApiProperty({ example: '9121111111', description: 'شماره تلفن ' })
  phone: string;
}

export class UserLoginInputDto {
  @Matches(/^\+\d{1,3}$/, {
    message: 'کد کشور باید با + شروع شود و شامل 1 تا 3 عدد باشد',
  })
  @IsNotEmpty({ message: '  کد کشور نمی تواند خالی باشد' })
  @ApiProperty({ example: '+98', description: 'کد کشور' })
  country_code: string;

  @Matches(/^\d{9,10}$/, {
    message: 'شماره تلفن معتبر نیست. باید 9 یا 10 رقمی باشد',
  })
  @IsNotEmpty({ message: ' شماره تلفن نمیتواند خالی باشد' })
  @ApiProperty({ example: '9121111111', description: 'شماره تلفن ' })
  phone: string;

  @MinLength(6)
  @MaxLength(6)
  @IsNotEmpty({ message: ' کد تایید  نمیتواند خالی باشد' })
  @ApiProperty({ example: '123456', description: 'کد تایید  ' })
  confirmation_code: string;

  @IsString()
  @IsNotEmpty({ message: '  device_id  نمی تواند خالی باشد' })
  @ApiProperty()
  device_id: string;
}
export class UserLoginOutputDto {
  @ApiProperty()
  @Mapped()
  access_token: string;
  @ApiProperty()
  @Mapped()
  refresh_token: string;
}

export class CheckPhoneRegisteredOutputDto {
  @ApiProperty({
    description: 'وضعیت پاسخ',
    enum: ['SendEmail', 'SendSms', 'GetEmail'],
    example: 'SendEmail',
  })
  status: 'SendEmail' | 'SendSms' | 'GetEmail';
  @ApiProperty()
  email: string;
}

export class RefreshTokenDto {
  @IsString()
  @ApiProperty({ example: 'refresh-token', description: 'رفرش توکن' })
  refresh_token: string;
}
