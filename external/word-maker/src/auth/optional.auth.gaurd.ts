import { CanActivate, ExecutionContext, Injectable } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request } from 'express';
import { TokenService } from '../token/token.service';

@Injectable()
export class JwtOptionalGuard implements CanActivate {
  constructor(
    private jwtService: JwtService,
    private tokenService: TokenService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest<Request>();
    const token = this.extractTokenFromHeader(request);
    if (!token || (await this.tokenService.isBlacklisted(token))) {
      request['user'] = null;
      return true;
    }

    try {
      const payload = await this.jwtService.verifyAsync(token, {
        secret: process.env.SECRET_KEY,
      });
      request['user'] = payload; // اگر توکن معتبر بود، مقداردهی کن
    } catch (error) {
      request['user'] = null; // اگر توکن نامعتبر بود، `user` رو `null` بذار
    }

    return true; // همیشه اجازه‌ی دسترسی بده
  }

  private extractTokenFromHeader(request: Request): string | undefined {
    const [type, token] = request.headers.authorization?.split(' ') ?? [];
    return type === 'Bearer' ? token : undefined;
  }
}
