import { Body, Controller, Post, Query, UseInterceptors } from '@nestjs/common';
import { AuthService } from './auth.service';
import {
  CheckPhoneRegisteredOutputDto,
  CheckRegisterPhoneInputDto,
  RefreshTokenDto,
  UserLoginInputDto,
  UserLoginOutputDto,
  UserRegistrationInputDto,
  UserRegistrationOutputDto,
} from './auth.dto';
import { MapInterceptor } from '../base/utils/mapper/mapper.interceptor';
import { ApiResponse, ApiTags } from '@nestjs/swagger';
import { UserTypeEnum } from '../base/enums/user.enums';

@Controller('auth')
@ApiTags('َAuth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('/is-phone-registered')
  @ApiResponse({
    status: 200,
    description: 'بررسی تکراری بودن اطلاعات',
    type: CheckPhoneRegisteredOutputDto,
  })
  async isPhoneRegistered(
    @Body() checkRegisterPhoneInputDto: CheckRegisterPhoneInputDto,
  ): Promise<CheckPhoneRegisteredOutputDto> {
    return await this.authService.isPhoneRegistered(checkRegisterPhoneInputDto);
  }

  @Post('/login')
  @UseInterceptors(MapInterceptor(UserLoginOutputDto))
  @ApiResponse({
    status: 200,
    type: UserLoginOutputDto,
  })
  async login(
    @Body() userLoginInputDto: UserLoginInputDto,
  ): Promise<UserLoginOutputDto> {
    const userInfo = await this.authService.signIn(userLoginInputDto);
    return userInfo;
  }

  @Post('/register')
  @ApiResponse({
    status: 200,
    type: UserRegistrationOutputDto,
  })
  @UseInterceptors(MapInterceptor(UserRegistrationOutputDto))
  async register(
    @Body() userRegistrationInputDto: UserRegistrationInputDto,
  ): Promise<UserRegistrationOutputDto> {
    return await this.authService.register({
      ...userRegistrationInputDto,
      userType: UserTypeEnum.GENERAL,
    });
  }

  @Post('/resend-confirmation-code')
  @ApiResponse({
    status: 200,
    type: UserRegistrationOutputDto,
  })
  @UseInterceptors(MapInterceptor(UserRegistrationOutputDto))
  async resendConfirmationCode(
    @Body() userRegistrationInputDto: UserRegistrationInputDto,
  ): Promise<UserRegistrationOutputDto> {
    return await this.authService.register({
      ...userRegistrationInputDto,
      userType: UserTypeEnum.GENERAL,
    });
  }

  @Post('refresh-token')
  @ApiResponse({
    status: 200,
    type: UserLoginOutputDto,
  })
  @UseInterceptors(MapInterceptor(UserLoginOutputDto))
  async refreshToken(
    @Body() refreshTokenDto: RefreshTokenDto,
  ): Promise<UserLoginOutputDto> {
    return this.authService.refreshToken(refreshTokenDto);
  }
}
