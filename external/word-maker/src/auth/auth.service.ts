import {
  BadRequestException,
  Injectable,
  NotFoundException,
  UnauthorizedException,
} from '@nestjs/common';
import { RoleEnum } from '../base/enums/user.enums';
import { JwtService } from '@nestjs/jwt';
import { UsersRepository } from '../user/user.repository';
import * as bcrypt from 'bcryptjs';
import {
  CheckPhoneRegisteredOutputDto,
  CheckRegisterPhoneInputDto,
  RefreshTokenDto,
  UserLoginInputDto,
  UserLoginOutputDto,
} from './auth.dto';
import { users } from '../base/database/entities/users.entity';
import { MessageService } from '../base/webServices/sms/message.service';
import { EmailService } from '../base/webServices/email/email.service';
import { VerifiedByEnum } from '../base/enums/user.enums';
import { UserRegistrationMasterInputDto } from '../admin/admin.dto';

@Injectable()
export class AuthService {
  constructor(
    private readonly usersRepository: UsersRepository,
    private readonly messageService: MessageService,
    private readonly emailService: EmailService,
    private jwtService: JwtService,
  ) {}
  async isPhoneRegistered(
    checkRegisterPhoneInputDto: CheckRegisterPhoneInputDto,
  ): Promise<CheckPhoneRegisteredOutputDto> {
    // check environment for dev
    if (process.env.NODE_ENV === 'development')
      return { status: 'SendSms', email: '<EMAIL>' };

    const { phone, country_code, country_name } = checkRegisterPhoneInputDto;
    const found = await this.usersRepository.checkExistByPhone(
      phone,
      country_code,
    );
    let result = new CheckPhoneRegisteredOutputDto();
    if (found || country_code === '+98') {
      const confirmation_code = await this.generateCodeAndSend(
        country_code,
        phone,
        found?.email,
      );
      if (!found) {
        await this.usersRepository.save(
          Object.assign(new users(), {
            country_name,
            country_code,
            phone,
            confirmation_code,
            verified_by: VerifiedByEnum.PHONE,
            roles: [RoleEnum.USER],
          }),
        );
        result.status = 'SendSms';
      } else {
        if (country_code === '+98') result.status = 'SendSms';
        else result = { status: 'SendEmail', email: found.email };
        await this.usersRepository.updateConfirmationCode(
          found.id,
          confirmation_code,
        );
      }
    } else result.status = 'GetEmail';
    return result;
  }

  async signIn(
    userLoginInputDto: UserLoginInputDto,
  ): Promise<UserLoginOutputDto> {
    const { phone, country_code, confirmation_code } = userLoginInputDto;
    const user = await this.usersRepository.login(country_code, phone);
    if (!user)
      throw new NotFoundException(`کاربر با این مشخصات یافت نشد  ${phone}`);

    if (
      !(await bcrypt.compare(confirmation_code, user.confirmation_code)) &&
      process.env.NODE_ENV !== 'development'
    )
      throw new UnauthorizedException(
        `کد تایید وارد شده اشتباه است ${phone} - ${confirmation_code}`,
      );

    const payload = {
      id: user.id,
      username: user.phone,
      fullName: user.name + ' ' + user.family,
      roles: user.roles,
    };

    const access_token = await this.jwtService.signAsync(payload);
    const refresh_token = await this.jwtService.signAsync(payload, {
      expiresIn: '30d',
    });

    await this.usersRepository.saveDevice(
      user.id,
      userLoginInputDto.device_id,
      refresh_token,
    );

    return {
      ...user,
      access_token,
      refresh_token,
    };
  }

  async register(
    userRegistrationInputDto: UserRegistrationMasterInputDto,
  ): Promise<users> {
    const { phone, email, country_code } = userRegistrationInputDto;
    const found = await this.usersRepository.checkExist(
      phone,
      country_code,
      email,
    );
    const confirmation_code = await this.generateCodeAndSend(
      country_code,
      phone,
      email,
    );
    if (!found) {
      if (email) {
        const found = await this.usersRepository.checkExistByEmail(email);
        if (found)
          throw new BadRequestException('ایمیل وارد شده تکراری می باشد');
      }
      const foundByPhone = await this.usersRepository.checkExistByPhone(
        phone,
        country_code,
      );
      if (foundByPhone)
        throw new BadRequestException('شماره تماس وارد شده تکراری می باشد');
      return await this.usersRepository.save(
        Object.assign(new users(), {
          ...userRegistrationInputDto,
          verified_by:
            userRegistrationInputDto.country_code === '+98'
              ? VerifiedByEnum.PHONE
              : VerifiedByEnum.EMAIL,
          confirmation_code,
          roles: [RoleEnum.USER],
        }),
      );
    } else
      await this.usersRepository.updateConfirmationCode(
        found.id,
        confirmation_code,
      );
    return found;
  }
  async generateCodeAndSend(country_code, phoneNumber, email) {
    const confirmation_code = Math.floor(100000 + Math.random() * 900000); //123456;
    this.sendVerificationCode(
      country_code,
      phoneNumber,
      email,
      confirmation_code,
    );

    const hash_confirmation_code = await bcrypt.hash(
      confirmation_code.toString(),
      12,
    );
    return hash_confirmation_code;
  }
  private sendVerificationCode(
    country_code,
    phoneNumber,
    email,
    confirmation_code,
  ) {
    if (country_code === '+98')
      this.messageService.sendSMS('0'.concat(phoneNumber), confirmation_code);
    else if (email) this.sendEmail(email, confirmation_code);
  }
  private async sendEmail(email, confirmation_code) {
    await this.emailService.sendEmail({
      to: email,
      subject: 'verification word-maker',
      text: `کد تایید شما جهت ورود ${confirmation_code}  می باشد `,
    });
  }

  async refreshToken(
    refreshTokenDto: RefreshTokenDto,
  ): Promise<UserLoginOutputDto> {
    const { refresh_token } = refreshTokenDto;
    const payload = this.jwtService.verify(refresh_token);

    const device = await this.usersRepository.findDeviceByIdAndRefreshToken(
      payload.id,
      refresh_token,
    );
    if (!device) {
      throw new UnauthorizedException('Invalid refresh token');
    }

    const newPayload = {
      id: device.user.id,
      username: device.user.phone,
      fullName: device.user.name + ' ' + device.user.family,
      roles: device.user.roles,
    };

    const access_token = await this.jwtService.signAsync(newPayload);
    const new_refresh_token = await this.jwtService.signAsync(newPayload, {
      expiresIn: '30d',
    });

    // ویرایش رفرش توکن جدید در دیتابیس
    await this.usersRepository.updateRefreshToken(device.id, new_refresh_token);

    return {
      ...device.user,
      access_token,
      refresh_token: new_refresh_token,
    };
  }
}
