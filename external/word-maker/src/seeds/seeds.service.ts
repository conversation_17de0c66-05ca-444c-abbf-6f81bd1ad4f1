import { Injectable } from '@nestjs/common';
import 'dotenv/config';
import { DataSource } from 'typeorm';
import { InjectDataSource } from '@nestjs/typeorm';
import { products } from '../base/database/entities/product.entity';
import { AccessLevelEnum } from '../base/enums/access-levels.enum';
@Injectable()
export class SeedsService {
  constructor(@InjectDataSource() private dataSource: DataSource) {}
  async saveProducts(): Promise<void> {
    await this.dataSource
      .createQueryBuilder()
      .insert()
      .into(products)
      .values([
        {
          price: 2400000,
          title: 'نسخه کامل',
          currency: 'IRR',
          description: 'دارای دسترسی تا مرحله آخر',
          accessLevel: AccessLevelEnum.COMPLETE,
          restrictedAccessLevels: [],
        },
      ])
      .execute();
  }
}
