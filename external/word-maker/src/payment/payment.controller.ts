import {
  Body,
  Controller,
  Get,
  Post,
  Query,
  UseGuards,
  Request,
  UseInterceptors,
  Param,
  ParseIntPipe,
} from '@nestjs/common';
import { PaymentService } from './payment.service';
import { ApiBearerAuth, ApiBody, ApiResponse, ApiTags } from '@nestjs/swagger';
import {
  CreatePaymentDto,
  CreatePaymentOutputDto,
  PaymentDiscountValidationInputDto,
  PaymentDiscountValidationOutputDto,
  PaymentOutputDto,
} from './payment.dto';
import { AuthGuard } from '../auth/auth.guard';
import { MapInterceptor } from '../base/utils/mapper/mapper.interceptor';

@Controller('payment')
@ApiTags('Payment')
export class PaymentController {
  constructor(private paymentService: PaymentService) {}

  @Post('/validate-discount')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiResponse({
    status: 200,
    description: 'اعتبارسنجی کد تخفیف برای پرداخت',
    type: PaymentDiscountValidationOutputDto,
  })
  async validateDiscountForPayment(
    @Body() dto: PaymentDiscountValidationInputDto,
  ): Promise<PaymentDiscountValidationOutputDto> {
    return await this.paymentService.validateDiscountForPayment(
      dto.product_id,
      dto.code,
    );
  }

  @Post()
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiBody({ type: CreatePaymentDto })
  @ApiResponse({
    status: 200,
    description: 'ثبت پرداخت کاربر',
    type: CreatePaymentOutputDto,
  })
  @UseInterceptors(MapInterceptor(CreatePaymentOutputDto))
  async createPayment(
    @Body() createPaymentDto: CreatePaymentDto,
    @Request() req,
  ) {
    return await this.paymentService.createPayment(createPaymentDto, req.user);
  }

  @Get('/:id')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiResponse({
    status: 200,
    description: 'اطلاعات پرداخت کاربر',
    type: PaymentOutputDto,
  })
  @UseInterceptors(MapInterceptor(PaymentOutputDto))
  async getPaymentById(
    @Param('id', ParseIntPipe) id: number,
    @Request() req,
  ): Promise<PaymentOutputDto> {
    return this.paymentService.getPaymentWithDiscountDetails(id, req.user.id);
  }

  @Get('/history/all')
  @ApiBearerAuth()
  @UseGuards(AuthGuard)
  @ApiResponse({
    status: 200,
    description: 'تاریخچه پرداخت‌های کاربر با جزئیات تخفیف',
    type: [PaymentOutputDto],
  })
  @UseInterceptors(MapInterceptor(PaymentOutputDto))
  async getPaymentHistory(@Request() req): Promise<PaymentOutputDto[]> {
    return this.paymentService.getPaymentHistory(req.user.id);
  }
}
