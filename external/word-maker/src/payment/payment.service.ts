import { HttpException, Injectable, NotFoundException } from '@nestjs/common';
import { PaymentsRepository } from './payment.repository';
import { LogService } from '../base/logger/log.service';
import * as ZarinpalCheckout from 'zarinpal-checkout';
import {
  PaymentStateEnum,
  PurchaseStateEnum,
} from '../base/enums/purchase-state.enum';
import {
  CreatePaymentDto,
  CreatePaymentOutputDto,
  PaymentDiscountValidationOutputDto,
  PaymentSaveInputDto,
} from './payment.dto';
import { ProductService } from '../product/product.service';
import { DiscountService } from '../discount/discount.service';
@Injectable()
export class PaymentService {
  private zarinpal;

  constructor(
    private readonly paymentsRepository: PaymentsRepository,
    private readonly logService: LogService,
    private readonly productService: ProductService,
    private readonly discountService: DiscountService,
  ) {
    this.zarinpal = ZarinpalCheckout.create(
      process.env.ZARIN_MERCHANT_ID,
      false,
    ); // true برای sandbox است
  }

  async createPayment(
    createPaymentDto: CreatePaymentDto,
    user: any,
  ): Promise<CreatePaymentOutputDto> {
    try {
      const { product_id, discount_code } = createPaymentDto;
      const product = await this.getProduct(product_id);
      let final_price = product.price;
      let discount_id;

      // Pre-validate discount code if provided
      if (discount_code) {
        const discountValidation =
          await this.discountService.checkDiscountCodeActive({
            code: discount_code,
          });

        if (!discountValidation.success) {
          throw new NotFoundException(discountValidation.message);
        }

        const { discount } = discountValidation;
        discount_id = discount.id;
        const discountAmount = (final_price * discount.discount_percent) / 100;
        final_price -= discountAmount;
      }

      const result = await this.zarinpal.PaymentRequest({
        Amount: final_price / 10,
        CallbackURL: createPaymentDto.callback_url,
        Description: 'خرید فایل APK',
        Email: '<EMAIL>',
        Mobile: 'asd',
      });

      const { authority, status } = result;
      if (status === 100) {
        // Save payment information to database
        const payment = Object.assign(new PaymentSaveInputDto(), {
          price: product.price,
          final_price,
          product_id,
          discount_id: discount_id ?? null,
          order_id: authority,
          user_id: user.id,
          state: PaymentStateEnum.SEND_TO_BANK,
        });

        const savedPayment = await this.paymentsRepository.save(payment);

        return {
          authority,
          url: `https://www.zarinpal.com/pg/StartPay/${authority}`,
          id: savedPayment.id,
        };
      } else {
        this.logService.createLog(
          'Failed to create payment-zarrinPal.',
          result.data,
        );
        throw new Error('Failed to create payment.');
      }
    } catch (e) {
      this.logService.createLog(`Failed to create payment. ${e.message}`, e);
      if (e instanceof HttpException) {
        throw e;
      }
      throw new Error(`Failed to create payment. ${e.message}`);
    }
  }
  private async getProduct(id: number) {
    const product = await this.productService.getProductById(id);
    if (!product) {
      this.logService.createLog('Product not found.', id.toString());
      throw new Error('Product not found.');
    }
    return product;
  }

  private async expireDiscountCode(discount_id: number) {
    await this.discountService.expireDiscount(discount_id);
  }

  async validateDiscountForPayment(
    productId: number,
    discountCode: string,
  ): Promise<PaymentDiscountValidationOutputDto> {
    try {
      // Get product details
      const product = await this.getProduct(productId);
      const originalPrice = product.price;

      // Validate discount code
      const discountValidation =
        await this.discountService.checkDiscountCodeActive({
          code: discountCode,
        });

      if (!discountValidation.success) {
        return {
          success: false,
          message: discountValidation.message,
        };
      }

      const discount = discountValidation.discount;
      const discountAmount = (originalPrice * discount.discount_percent) / 100;
      const finalPrice = originalPrice - discountAmount;

      return {
        success: true,
        message: 'کد تخفیف معتبر است',
        discount_details: {
          code: discount.code,
          percent: discount.discount_percent,
          discount_amount: discountAmount,
          original_price: originalPrice,
          final_price: finalPrice,
          currency: product.currency,
        },
      };
    } catch (error) {
      return {
        success: false,
        message: error.message || 'خطا در اعتبارسنجی کد تخفیف',
      };
    }
  }

  async getPaymentWithDiscountDetails(id: number, user_id: number) {
    const payment = await this.paymentsRepository.findOneById(id, user_id);
    if (!payment) {
      throw new Error('Payment not found.');
    }

    // Add discount details if discount was used
    if (payment.discount) {
      const discountAmount =
        (payment.price * payment.discount.discount_percent) / 100;
      return {
        ...payment,
        discount_code: payment.discount.code,
        discount_percent: payment.discount.discount_percent,
        discount_amount: discountAmount,
      };
    }

    return payment;
  }

  async getPaymentHistory(user_id: number) {
    const payments = await this.paymentsRepository.findByUserId(user_id);

    return payments.map((payment) => {
      if (payment.discount) {
        const discountAmount =
          (payment.price * payment.discount.discount_percent) / 100;
        return {
          ...payment,
          discount_code: payment.discount.code,
          discount_percent: payment.discount.discount_percent,
          discount_amount: discountAmount,
        };
      }
      return payment;
    });
  }
}
