# Tech — Stack & Practices (Condensed)

Runtime & language

- Node.js 18+, TypeScript (strict), target ES2017.

Framework & architecture

- NestJS 10+ (modular monolith), Express under @nestjs/platform-express.

Data & caching

- PostgreSQL (primary datastore) + TypeORM 0.3.x

- Redis for sessions, refresh tokens, and queues

Key libraries & integrations

- Auth/security: @nestjs/jwt, passport, bcryptjs, class-validator

- Payments/integrations: zarinpal-checkout, Cafe Bazaar integration (OAuth2), SibApp (secret-key API)

- Communication: nestjs-soap (SMS), nodemailer (email), @nestjs/axios

- Utilities: rxjs, reflect-metadata, moment (limited)

Dev & build tooling

- ESLint + Prettier; TypeScript ESLint

- NestJS CLI, ts-node for local dev

- Webpack + fork-ts-checker for HMR in dev

Environment & configuration

- `.env.development` for dev; `.env` for production

- Config via `@nestjs/config` with validation

- Example TypeORM options (managed by env vars): host, port, username, password, database, entities path, migrations path, migrationsRun: true

Required environment variables (examples)

- PG_HOST, PG_PORT, PG_USER, PG_PASSWORD, PG_DATABASE

- REDIS_HOST, REDIS_PORT, REDIS_URL

- SECRET_KEY (JWT)

- ZARIN_MERCHANT_ID, BAZAR_CLIENT_ID, BAZAR_CLIENT_SECRET, AIB_APP_TOKEN

- SMS_WSDL, SMS_USERNAME, SMS_PASSWORD, SMS_FROM

- LOCALHOST_URL, ADMIN_USERNAMES

Testing & quality

- Jest for tests (project historically indicates tests but policy varied); run `npm run test` and `npm run test:cov` where applicable.

- `npm run lint` required before finalizing changes.

Deployment & migrations

- Docker multi-stage images; Compose for local services.

- TypeORM CLI used for migrations; migrations stored in source control and auto-run if configured.

Operational notes

- Keep secrets in environment variables.

- Monitor gateway token expiry and implement automatic refresh for OAuth-based integrations (Cafe Bazaar).

- Ensure idempotency and safe concurrency for discount consumption and payment verification flows.