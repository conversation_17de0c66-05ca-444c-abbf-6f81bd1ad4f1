# Context — Current State (Condensed)

Project status

- Active development and maintenance. Local dev server typically on port 3002 with Swagger at `/api/docs`.

Recent changes (high level)

- RBAC added: `roles` on users (ADMIN, AFFILIATE, USER); `@Roles()` decorator and `RolesGuard`; JWT payload includes roles (migration applied).

- Payments: multi-gateway flows implemented for ZarinPal, Cafe Bazaar, and SibApp; payment/purchase state tracking and discount consumption/rollback.

- Affiliate module: registration, update, retrieval, admin listing; affiliate_info table and relations added (migrations applied).

- DB schema: added SibApp support fields and several migrations improving relations and constraints.

Technical state

- PostgreSQL with TypeORM; migrations run automatically on startup in dev.

- Redis used for sessions/refresh tokens and queues.

- JWT tokens configured (15-hour access, 30-day refresh).

- Swagger UI available (Bearer auth configured).

- Key endpoints: auth (register/login/refresh), user, products, payments (create/verify), purchase verification, discounts, admin, affiliates.

Next steps & technical debt

- Monitor RBAC and collect feedback.

- End-to-end test multi-gateway payment flows in production-like environment.

- Optimize discount-handling for scale.

- Remove legacy AdminGuard/PhoneCheckInterceptor after final RBAC migration.

- Consolidate error handling patterns and logging.

Development environment

- Docker Compose for local PostgreSQL/Redis, hot reload via NestJS watch mode, `.env.development` used for local config.

Maintenance notes

- Consolidated and de-duplicated the Memory Bank files: merged overlapping content, standardized terminology (RBAC, JWT, Redis, TypeORM, ZarinPal, Cafe Bazaar, SibApp, DTO, Guard, Module, Service, Repository), and compressed each file to prescribed lengths. Created a `tasks.md` entry documenting the update workflow and added a short post-update checklist. Timestamp: 2025-09-20T15:14:50Z.