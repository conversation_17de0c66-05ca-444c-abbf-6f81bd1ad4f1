## Task: Consolidate & Standardize Memory Bank

**Last performed:** 2025-09-20T15:14:50Z

Purpose

- Consolidate, de-duplicate, standardize terminology, and compress the Memory Bank core files.

Files to review

- `.kilocode/rules/memory-bank/brief.md`

- `.kilocode/rules/memory-bank/product.md`

- `.kilocode/rules/memory-bank/context.md`

- `.kilocode/rules/memory-bank/architecture.md`

- `.kilocode/rules/memory-bank/tech.md`

- `.kilocode/rules/memory-bank/memory-bank-instructions.md`

Steps

1. Read all memory-bank files and relevant recent migrations/README for context.

2. Extract overlapping information and map topics to target files (brief ← high-level; context ← current facts; architecture/tech ← technical detail).

3. Standardize terminology list and replace inconsistent variants.

4. Compress text to meet word/length targets and remove duplicate paragraphs.

5. Add a single maintenance note to `context.md` with ISO timestamp.

6. Draft `tasks.md` entry documenting the workflow and last-performed timestamp.

7. Deliver drafts to reviewer; incorporate feedback and finalize.

QA checklist

- Ensure `brief.md` reflects authoritative scope.

- Confirm `context.md` is factual and short; maintenance note included.

- Check that architecture & tech contain no redundant cross-copy.

- Validate consistent use of standardized terms.

Important notes & gotchas

- Do not modify source code during this workflow.

- If a new persistent change is detected, recommend a Memory Bank update and list files affected.