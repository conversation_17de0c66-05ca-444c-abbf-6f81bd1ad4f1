# Product — WordMaker (Condensed)

Purpose

- Backend platform enabling secure distribution and monetization of digital/mobile products.

Problems solved & capabilities

- Secure digital delivery: product catalog, pricing, access control by role.

- Multi-gateway payments: ZarinPal (Iran), Cafe Bazaar (Android store), SibApp (alternate store) — handle creation, verification, refunds, state tracking.

- Regional authentication: phone + SMS for Iranian users; email + verification for international users (auto-detected by country code).

- Growth features: discount codes, referral program, affiliate tracking and commissions.

- Admin operations: user/product/discount management and analytics.

User experience goals

- Seamless onboarding (auto-select auth method by region).

- Secure, fast purchases with immediate access on verification.

- Simple discount application and referral flows.

- Comprehensive admin and affiliate insights.

Target audiences

- Primary: Iranian mobile app developers

- Secondary: Regional/international developers and digital vendors

Success metrics (examples)

- Conversion by auth method, payment success rates per gateway, discount redemption rate, revenue and retention metrics.