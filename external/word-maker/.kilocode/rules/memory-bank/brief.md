# Brief — WordMaker Backend

WordMaker is a modular-monolith NestJS backend for selling digital products (APK/mobile apps). It provides user authentication, product catalog, payments, purchase verification, discounts, affiliate/referral features, and an admin surface.

Core technologies

- NestJS (v10+), TypeScript, Node.js 18+

- PostgreSQL with TypeORM

- Redis for caching and session/refresh-token storage

- JWT for authentication

- Swagger/OpenAPI for API docs

- Docker for development/production packaging

Primary modules

- Auth, User, Product, Purchase, Payment, Discount, Affiliate, Admin, Base (logger, database, web services)

Development standards

- Strict TypeScript, DTOs with class-validator for input validation

- Repository, Service, and Module patterns; Guards for auth/RBAC

- Migrations tracked (TypeORM); automatic migration execution in dev

- ESLint + Prettier enforced; run `npm run lint` before finalizing changes