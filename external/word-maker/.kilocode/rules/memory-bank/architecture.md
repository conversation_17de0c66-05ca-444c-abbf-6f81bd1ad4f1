# Architecture — WordMaker (Condensed)

High-level architecture

- Modular monolith (NestJS). Layers:

  - API Layer: Controllers + Swagger

  - Business Layer: Services, Guards, DTOs

  - Data Layer: Repositories (TypeORM), Entities, Migrations

  - Infrastructure: PostgreSQL, Redis, external web services (payment gateways, SMS, email), logging

Core design patterns

- Repository: `src/*/*.repository.ts` — TypeORM repositories abstract DB access.

- Service: `src/*/*.service.ts` — business logic and orchestration.

- Guard: `src/auth/*.guard.ts` — AuthGuard (JWT), RolesGuard (RBAC), legacy AdminGuard (deprecated).

- DTO: `src/*/*.dto.ts` with class-validator for validation and Swagger metadata.

- Module: feature encapsulation via NestJS Modules exporting Services/Repositories.

Source structure (top-level highlights)

- `src/auth`, `src/user`, `src/product`, `src/purchase`, `src/payment`, `src/discount`, `src/affiliate`, `src/admin`, `src/base` (database/entities, logger, webServices).

Key technical decisions

- Authentication: JWT (15h access / 30d refresh), device-based refresh storage, phone vs email flow by country code, verification via SMS or email.

- Database: PostgreSQL with TypeORM; migrations under `src/base/database/migrations`, migrations run automatically in dev; entities under `src/base/database/entities`.

- Payments: Multi-gateway integration (ZarinPal, Cafe Bazaar, SibApp). Two-step verification pattern (create → verify) with gateway-specific flows and token refresh for gateways requiring OAuth.

- Logging & Errors: Global exception filter (`AllExceptionsFilter`) and structured, persistent logs.

- Validation & Security: DTO-based validation, RBAC via `RolesGuard` + decorator, environment-managed secrets.

Critical implementation paths

- User registration: AuthController.register → AuthService → generate & send code (SMS/Email) → store hashed code → create/update user.

- Sign-in: AuthController.signIn → AuthService.validate code → generate JWTs → store device refresh token.

- Payment creation: PaymentController.createPayment → PaymentService validate product/discount → create gateway request (ZarinPal common path) → save payment record → return URL.

- Purchase verification: PurchaseController.verifyPurchase → PurchaseService route to gateway-specific verification → update payments/purchases and apply/rollback discounts.

- RBAC check: Incoming request → AuthGuard (JWT) → RolesGuard → allow/deny.

Component relationships (summary)

- App Module imports feature Modules (Auth, User, Payment, Purchase, Discount, Product, Affiliate, Admin).

- Services call Repositories; Controllers call Services.

- WebServices (bazar, sibApp, sms, email) are used by Payment and Auth services.

Security & performance

- Input validation (class-validator), prepared statements via TypeORM.

- Redis for session/refresh-token storage and caching.

- Connection pooling via TypeORM; lazy-loading patterns where appropriate.

- Considerations: scale discount consumption under high concurrency, ensure idempotent verification endpoints.

Deployment

- Local dev: Docker Compose (Postgres + Redis), NestJS watch mode.

- Production: multi-stage Docker builds, environment-specific config, process manager (PM2) recommended, container orchestration ready.